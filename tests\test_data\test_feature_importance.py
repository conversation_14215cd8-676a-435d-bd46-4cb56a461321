"""LightGBM特征重要性评估测试模块

本模块专门测试特征选择器中的LightGBM特征重要性评估功能，包括：
1. 特征重要性计算
2. 重要性阈值筛选
3. 特征排序
4. 异常情况处理
5. 维度约束处理

相关模块:
1. 被测试模块:
   - src/data/preprocessing/feature_selector.py: 特征选择器实现
2. 依赖模块:
   - lightgbm: LightGBM模型库
   - src/utils/config_manager.py: 配置管理
   - src/utils/logger.py: 日志系统
"""

from typing import Any
from unittest.mock import MagicMock, patch

import lightgbm as lgb
import numpy as np
import pandas as pd
import pytest

from src.data.preprocessing.feature_selector import FeatureSelector

# from src.utils.config_manager import ConfigManager
from src.utils.logger import LoggerFactory


@pytest.fixture
def sample_config() -> dict[str, Any]:
    """创建测试配置"""
    return {
        'model': {
            'n_heads': 4
        },
        'feature_selection': {
            'importance_threshold': 0.05,
            'lagged_corr': {
                'min_abs_corr': 0.5,
                'max_lag': 28
            },
            'noise_detection': {
                'low_variance_threshold': 0.04,
                'high_correlation_threshold': 0.99
            }
        }
    }


@pytest.fixture
def sample_dataframe() -> pd.DataFrame:
    """创建测试数据集，包含10个特征和目标列value15"""
    np.random.seed(42)
    n_samples = 100

    # 创建特征
    data = {}
    for i in range(10):
        data[f'feature_{i}'] = np.random.randn(n_samples)

    # 创建目标变量，与前3个特征相关
    data['value15'] = (
        data['feature_0'] * 0.5 +
        data['feature_1'] * 0.3 +
        data['feature_2'] * 0.2 +
        np.random.randn(n_samples) * 0.1
    )

    return pd.DataFrame(data)


@pytest.mark.parametrize("importance_threshold", [0.05, 0.1, 0.15])
def test_importance_threshold_filtering(sample_dataframe, importance_threshold):
    """测试不同重要性阈值下的特征筛选效果"""
    # 1. 准备测试环境
    mock_config = MagicMock()
    # 创建嵌套的MagicMock对象
    mock_config.feature_selection = MagicMock()
    mock_config.feature_selection.lagged_corr = MagicMock()
    mock_config.model = MagicMock()
    # 添加noise_detection配置
    mock_config.feature_selection.noise_detection = MagicMock()
    mock_config.feature_selection.noise_detection.low_variance_threshold = 0.01  # 设置较低的方差阈值
    mock_config.feature_selection.noise_detection.high_correlation_threshold = 0.9
    # 设置重要性阈值
    mock_config.feature_selection.importance_threshold = importance_threshold
    mock_config.feature_selection.lagged_corr.min_abs_corr = 0.0  # 禁用滞后相关性筛选
    mock_config.model.n_heads = 4

    logger_factory = LoggerFactory()
    selector = FeatureSelector(mock_config, logger_factory)

    # 2. 模拟滞后相关性计算，返回高相关性以便所有特征通过滞后相关性筛选
    with patch.object(FeatureSelector, '_calculate_lagged_correlations') as mock_calc_lagged_corrs:
        feature_names = [col for col in sample_dataframe.columns if col != 'value15']
        mock_calc_lagged_corrs.return_value = dict.fromkeys(feature_names, 0.9)

        # 3. 执行特征选择
        selected_features = selector.select(sample_dataframe)

        # 4. 验证结果
        assert isinstance(selected_features, list), "应返回特征名称列表"
        assert len(selected_features) > 0, "应至少选择一个特征"
        assert len(selected_features) <= len(feature_names), "选择的特征数不应超过原始特征数"

        # 5. 验证重要性阈值效果
        # 阈值越高，选择的特征应该越少
        if importance_threshold > 0.1:
            assert len(selected_features) < len(feature_names), f"阈值{importance_threshold}应筛选掉部分特征"


def test_importance_calculation_correctness():
    """测试特征重要性计算的正确性"""
    # 1. 创建简单的测试数据，特征与目标的关系明确
    np.random.seed(42)
    n_samples = 200

    # 创建特征
    X = pd.DataFrame({
        'strong_feature': np.random.randn(n_samples),  # 强相关特征
        'medium_feature': np.random.randn(n_samples),  # 中等相关特征
        'weak_feature': np.random.randn(n_samples),    # 弱相关特征
        'noise_feature': np.random.randn(n_samples)    # 噪声特征
    })

    # 创建目标变量，与特征的关系明确
    y = (
        X['strong_feature'] * 0.8 +
        X['medium_feature'] * 0.4 +
        X['weak_feature'] * 0.1 +
        np.random.randn(n_samples) * 0.05
    )

    # 2. 直接使用LightGBM计算特征重要性
    model = lgb.LGBMRegressor(**FeatureSelector.DEFAULT_LGBM_PARAMS)
    model.fit(X, y)

    # 获取特征重要性
    importances = pd.Series(model.feature_importances_, index=X.columns)
    importances = importances / importances.sum()  # 归一化

    # 3. 验证重要性排序是否符合预期
    sorted_features = importances.sort_values(ascending=False).index.tolist()

    # 预期排序：强相关 > 中等相关 > 弱相关 > 噪声
    expected_order = ['strong_feature', 'medium_feature', 'weak_feature', 'noise_feature']

    # 验证排序是否基本符合预期（允许一定的随机性）
    assert sorted_features[0] == expected_order[0], "最重要的特征应该是strong_feature"
    assert sorted_features[-1] == expected_order[-1], "最不重要的特征应该是noise_feature"


def test_importance_with_missing_values():
    """测试包含缺失值的数据集上的特征重要性评估"""
    # 1. 创建包含缺失值的测试数据
    np.random.seed(42)
    n_samples = 100

    # 创建特征 - 增加到10个特征，确保即使有缺失值移除后仍有足够的特征
    data = {}
    for i in range(10):
        values = np.random.randn(n_samples) * 2.0  # 增大方差
        # 随机添加缺失值，但只在部分特征中添加
        if i < 3:  # 只在前3个特征中添加缺失值
            mask = np.random.random(n_samples) < 0.1  # 10%的缺失率
            values[mask] = np.nan
        data[f'feature_{i}'] = values

    # 创建目标变量
    data['value15'] = np.random.randn(n_samples)

    df = pd.DataFrame(data)

    # 2. 准备测试环境
    mock_config = MagicMock()
    mock_config.feature_selection = MagicMock()
    mock_config.feature_selection.lagged_corr = MagicMock()
    mock_config.model = MagicMock()
    # 添加noise_detection配置
    mock_config.feature_selection.noise_detection = MagicMock()
    mock_config.feature_selection.noise_detection.low_variance_threshold = 0.01  # 设置较低的方差阈值
    mock_config.feature_selection.noise_detection.high_correlation_threshold = 0.9
    mock_config.feature_selection.importance_threshold = 0.05
    mock_config.feature_selection.lagged_corr.min_abs_corr = 0.0
    mock_config.model.n_heads = 4

    logger_factory = LoggerFactory()
    selector = FeatureSelector(mock_config, logger_factory)

    # 3. 模拟滞后相关性计算
    with patch.object(FeatureSelector, '_calculate_lagged_correlations') as mock_calc_lagged_corrs:
        feature_names = [col for col in df.columns if col != 'value15']
        mock_calc_lagged_corrs.return_value = dict.fromkeys(feature_names, 0.9)

        # 4. 执行特征选择
        selected_features = selector.select(df)

        # 5. 验证结果
        assert isinstance(selected_features, list), "应返回特征名称列表"
        # 即使有缺失值，也应该能选择特征
        assert len(selected_features) > 0, "应至少选择一个特征"


def test_importance_for_dimension_constraint(sample_dataframe):
    """测试维度约束下的特征重要性排序"""
    # 1. 准备测试环境
    mock_config = MagicMock()
    mock_config.feature_selection = MagicMock()
    mock_config.feature_selection.lagged_corr = MagicMock()
    mock_config.model = MagicMock()
    # 添加noise_detection配置
    mock_config.feature_selection.noise_detection = MagicMock()
    mock_config.feature_selection.noise_detection.low_variance_threshold = 0.01  # 设置较低的方差阈值
    mock_config.feature_selection.noise_detection.high_correlation_threshold = 0.9
    mock_config.feature_selection.importance_threshold = 0.0  # 禁用重要性阈值筛选
    mock_config.feature_selection.lagged_corr.min_abs_corr = 0.0  # 禁用滞后相关性筛选
    mock_config.model.n_heads = 4  # 设置n_heads=4，使得(k+1)%4==0的约束生效

    logger_factory = LoggerFactory()
    selector = FeatureSelector(mock_config, logger_factory)

    # 2. 模拟滞后相关性计算
    with patch.object(FeatureSelector, '_calculate_lagged_correlations') as mock_calc_lagged_corrs:
        feature_names = [col for col in sample_dataframe.columns if col != 'value15']
        mock_calc_lagged_corrs.return_value = dict.fromkeys(feature_names, 0.9)

        # 3. 模拟LightGBM特征重要性，设置明确的重要性顺序
        with patch('lightgbm.LGBMRegressor') as mock_lgbm:
            mock_lgbm_instance = mock_lgbm.return_value
            mock_lgbm_instance.fit.return_value = None

            # 设置递减的特征重要性
            importances = np.linspace(0.9, 0.1, len(feature_names))
            mock_lgbm_instance.feature_importances_ = importances

            # 4. 执行特征选择
            selected_features = selector.select(sample_dataframe)

            # 5. 验证结果
            # 由于n_heads=4，应选择满足(k+1)%4==0的特征数，即k=3,7,11...
            expected_count = 7  # 最接近10的满足条件的k值
            assert len(selected_features) == expected_count, f"应选择{expected_count}个特征以满足维度约束"


def test_importance_calculation_failure(sample_dataframe):
    """测试当特征重要性计算失败时应抛出异常，不再回退到滞后相关性排序"""
    # 1. 准备测试环境
    mock_config = MagicMock()
    mock_config.feature_selection = MagicMock()
    mock_config.feature_selection.lagged_corr = MagicMock()
    mock_config.model = MagicMock()
    # 添加noise_detection配置
    mock_config.feature_selection.noise_detection = MagicMock()
    mock_config.feature_selection.noise_detection.low_variance_threshold = 0.01  # 设置较低的方差阈值
    mock_config.feature_selection.noise_detection.high_correlation_threshold = 0.9
    mock_config.feature_selection.importance_threshold = 0.05
    mock_config.feature_selection.lagged_corr.min_abs_corr = 0.0
    mock_config.model.n_heads = 4

    logger_factory = LoggerFactory()
    selector = FeatureSelector(mock_config, logger_factory)

    # 2. 模拟滞后相关性计算
    with patch.object(FeatureSelector, '_calculate_lagged_correlations') as mock_calc_lagged_corrs:
        feature_names = [col for col in sample_dataframe.columns if col != 'value15']
        # 设置滞后相关性
        corrs = dict.fromkeys(feature_names, 0.9)
        mock_calc_lagged_corrs.return_value = corrs

        # 3. 模拟LightGBM特征重要性计算失败
        with patch('lightgbm.LGBMRegressor') as mock_lgbm:
            mock_lgbm_instance = mock_lgbm.return_value
            mock_lgbm_instance.fit.side_effect = Exception("模拟LightGBM训练失败")

            # 4. 执行特征选择，应抛出异常
            with pytest.raises(Exception) as excinfo:  # 使用更通用的Exception捕获
                selector.select(sample_dataframe)

            # 5. 验证异常信息
            assert "模拟LightGBM训练失败" in str(excinfo.value), "应抛出模拟LightGBM训练失败的异常"


def test_empty_importance_values(sample_dataframe):
    """测试当模型重要性为空或全为NaN时应抛出异常，不再回退到滞后相关性排序"""
    # 1. 准备测试环境
    mock_config = MagicMock()
    mock_config.feature_selection = MagicMock()
    mock_config.feature_selection.lagged_corr = MagicMock()
    mock_config.model = MagicMock()
    # 添加noise_detection配置
    mock_config.feature_selection.noise_detection = MagicMock()
    mock_config.feature_selection.noise_detection.low_variance_threshold = 0.01  # 设置较低的方差阈值
    mock_config.feature_selection.noise_detection.high_correlation_threshold = 0.9
    mock_config.feature_selection.importance_threshold = 0.0  # 设置为0，确保不会因重要性阈值筛选掉特征
    mock_config.feature_selection.lagged_corr.min_abs_corr = 0.0
    mock_config.model.n_heads = 4

    logger_factory = LoggerFactory()
    selector = FeatureSelector(mock_config, logger_factory)

    # 2. 模拟滞后相关性计算
    with patch.object(FeatureSelector, '_calculate_lagged_correlations') as mock_calc_lagged_corrs:
        feature_names = [col for col in sample_dataframe.columns if col != 'value15']
        # 设置滞后相关性
        corrs = dict.fromkeys(feature_names, 0.9)
        mock_calc_lagged_corrs.return_value = corrs

        # 3. 模拟LightGBM特征重要性计算返回NaN值
        with patch('lightgbm.LGBMRegressor') as mock_lgbm:
            mock_lgbm_instance = mock_lgbm.return_value
            mock_lgbm_instance.fit.return_value = None
            # 设置特征重要性为NaN
            mock_lgbm_instance.feature_importances_ = np.array([np.nan] * len(feature_names))

            # 4. 执行特征选择，应抛出异常
            with pytest.raises(ValueError) as excinfo:
                # 首先需要通过模型重要性评估，然后在维度约束处理时因为importances全为NaN而抛出异常
                selector.select(sample_dataframe)

            # 5. 验证异常信息
            assert "模型重要性" in str(excinfo.value) and "无效" in str(excinfo.value), "应抛出模型重要性无效的异常"
