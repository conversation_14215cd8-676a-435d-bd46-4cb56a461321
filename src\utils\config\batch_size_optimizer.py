"""批次大小优化器配置读取模块 - 专门处理动态批次大小调整的配置读取

此模块提供了一个专业的配置读取类，用于从配置对象中读取和验证动态批次大小调整的配置。
它遵循以下设计原则：
1. 关注点分离：将配置读取逻辑与业务逻辑分离
2. 严格验证：对配置项进行严格的类型和值验证
3. 详细日志：记录配置读取过程中的警告和错误
4. 向后兼容：支持旧的配置项格式
"""

from __future__ import annotations

import logging
from dataclasses import dataclass  # Ensure dataclass and field are imported
from typing import Any

# The import "from src.utils.config.training import BatchSizeOptimizerConfig" is problematic
# if BatchSizeOptimizerConfig is defined in this file.
# It should be defined here and then imported by training.py or other modules.
# For now, I will assume BatchSizeOptimizerConfig is defined below.
from src.utils.config.base import BaseConfig  # Import BaseConfig
from src.utils.cuda import cuda_manager  # For BatchSizeOptimizer
from src.utils.cuda.manager import CUDAManager  # For BatchSizeOptimizer
from src.utils.logger import get_logger


@dataclass
class BatchSizeOptimizerConfig(BaseConfig):
    """批次大小优化器配置"""
    enabled: bool
    initial_batch_size: int
    min_batch_size: int
    max_batch_size: int
    memory_utilization_target: float
    strategy: str
    adjustment_interval: int
    growth_factor: float
    shrink_factor: float
    stability_threshold: int
    warmup_steps: int
    oom_recovery: bool
    # Inherited from BaseConfig, and also explicitly passed by create_batch_size_optimizer_config
    # dimensions: Dict[str, Any] = field(default_factory=dict) # Already in BaseConfig
    # noise_dim: int = 64 # Already in BaseConfig

    def __post_init__(self):
        super().__post_init__() # Call BaseConfig's post_init
        get_logger(__name__)

        required_fields = [
            'enabled', 'initial_batch_size', 'min_batch_size', 'max_batch_size',
            'memory_utilization_target', 'strategy', 'adjustment_interval',
            'growth_factor', 'shrink_factor', 'stability_threshold',
            'warmup_steps', 'oom_recovery'
        ]
        # Check if all required fields are present (not None, as they are not Optional anymore)
        missing_fields = [f_name for f_name in required_fields if not hasattr(self, f_name) or getattr(self, f_name) is None]
        if missing_fields:
            raise ValueError(f"BatchSizeOptimizerConfig 缺失必需字段或字段为 None: {', '.join(missing_fields)}")

        if not isinstance(self.enabled, bool):
            raise ValueError(f"enabled 必须是布尔类型, 得到 {type(self.enabled)}")
        if not isinstance(self.initial_batch_size, int) or self.initial_batch_size <= 0:
            raise ValueError(f"initial_batch_size 必须是正整数, 得到 {self.initial_batch_size}")
        if not isinstance(self.min_batch_size, int) or self.min_batch_size <= 0:
            raise ValueError(f"min_batch_size 必须是正整数, 得到 {self.min_batch_size}")
        if not isinstance(self.max_batch_size, int) or self.max_batch_size <= 0:
            raise ValueError(f"max_batch_size 必须是正整数, 得到 {self.max_batch_size}")
        if not isinstance(self.memory_utilization_target, float) or not (0 < self.memory_utilization_target <= 1):
            raise ValueError(f"memory_utilization_target 必须是 (0, 1] 范围内的浮点数, 得到 {self.memory_utilization_target}")
        if not isinstance(self.strategy, str) or self.strategy not in ["memory", "performance", "hybrid"]:
            raise ValueError(f"strategy 必须是 'memory', 'performance', 或 'hybrid', 得到 {self.strategy}")
        if not isinstance(self.adjustment_interval, int) or self.adjustment_interval <= 0:
            raise ValueError(f"adjustment_interval 必须是正整数, 得到 {self.adjustment_interval}")
        if not isinstance(self.growth_factor, float) or self.growth_factor <= 1.0:
            raise ValueError(f"growth_factor 必须是大于1.0的浮点数, 得到 {self.growth_factor}")
        if not isinstance(self.shrink_factor, float) or not (0 < self.shrink_factor < 1.0):
            raise ValueError(f"shrink_factor 必须是 (0, 1) 范围内的浮点数, 得到 {self.shrink_factor}")
        if not isinstance(self.stability_threshold, int) or self.stability_threshold <= 0:
            raise ValueError(f"stability_threshold 必须是正整数, 得到 {self.stability_threshold}")
        if not isinstance(self.warmup_steps, int) or self.warmup_steps < 0:
            raise ValueError(f"warmup_steps 必须是非负整数, 得到 {self.warmup_steps}")
        if not isinstance(self.oom_recovery, bool):
            raise ValueError(f"oom_recovery 必须是布尔类型, 得到 {type(self.oom_recovery)}")

        if self.min_batch_size > self.initial_batch_size:
            raise ValueError(f"min_batch_size ({self.min_batch_size}) 不能大于 initial_batch_size ({self.initial_batch_size})")
        if self.initial_batch_size > self.max_batch_size:
            raise ValueError(f"initial_batch_size ({self.initial_batch_size}) 不能大于 max_batch_size ({self.max_batch_size})")


class BatchSizeOptimizerConfigReader:
    """批次大小优化器配置读取器

    负责从配置对象中读取和验证动态批次大小调整的配置。
    支持从不同格式的配置对象中读取配置，包括ConfigManager和字典。
    """

    def __init__(self, logger: logging.Logger | None = None):
        """初始化配置读取器

        Args:
            logger: 日志记录器，如果为None则创建新的记录器
        """
        self.logger = logger or get_logger("BatchSizeOptimizerConfigReader")

    def read_dynamic_batch_size_enabled(self, config: Any) -> bool:
        """读取动态批次大小启用状态

        首先检查新的配置项（batch_size_optimizer.enabled），
        然后才检查旧的配置项（dynamic_batch_size）。

        Args:
            config: 配置对象，可以是ConfigManager或字典

        Returns:
            bool: 动态批次大小是否启用

        Raises:
            ValueError: 如果配置项不存在或类型不正确
        """
        source = "training.batch_size_optimizer.enabled"
        try:
            # 直接尝试访问配置项
            if hasattr(config, 'training') and hasattr(config.training, 'batch_size_optimizer') and hasattr(config.training.batch_size_optimizer, 'enabled'):
                enabled = config.training.batch_size_optimizer.enabled
                self.logger.debug(f"从对象配置的 {source} 读取到配置值: {enabled}")
            elif isinstance(config, dict) and 'training' in config and isinstance(config['training'], dict) and \
                 'batch_size_optimizer' in config['training'] and isinstance(config['training']['batch_size_optimizer'], dict) and \
                 'enabled' in config['training']['batch_size_optimizer']:
                enabled = config['training']['batch_size_optimizer']['enabled']
                self.logger.debug(f"从字典配置的 {source} 读取到配置值: {enabled}")
            else:
                # 如果找不到，则抛出错误
                error_msg = f"未在配置中找到必需的配置项: {source}"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # 验证类型
            if not isinstance(enabled, bool):
                error_msg = f"配置项 {source} 的值必须是布尔类型，但获取到的是: {type(enabled)}"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            return enabled

        except (AttributeError, KeyError, TypeError) as e:
            error_msg = f"访问必需的配置项 {source} 失败: {e!s}"
            self.logger.error(error_msg)
            raise ValueError(error_msg) from e
        except Exception as e:
            # 捕获其他潜在错误
            if isinstance(e, ValueError): # 避免重复包装 ValueError
                raise
            error_msg = f"读取动态批次大小配置时发生意外错误: {e!s}"
            self.logger.error(error_msg)
            raise ValueError(error_msg) from e

    def read_batch_size_optimizer_config(self, config: Any) -> dict[str, Any]:
        """读取批次大小优化器配置

        Args:
            config: 配置对象，可以是ConfigManager或字典

        Returns:
            Dict[str, Any]: 批次大小优化器配置字典

        Raises:
            ValueError: 如果配置项不存在或读取失败
        """
        optimizer_config = {}
        required_fields = [
            'enabled', 'initial_batch_size', 'min_batch_size', 'max_batch_size',
            'memory_utilization_target', 'strategy', 'adjustment_interval',
            'growth_factor', 'shrink_factor', 'stability_threshold',
            'warmup_steps', 'oom_recovery'
        ]

        try:
            if hasattr(config, 'training') and hasattr(config.training, 'batch_size_optimizer'):
                bso_config_obj = config.training.batch_size_optimizer # This should be BatchSizeOptimizerConfig instance
                missing_fields = []
                for field_name in required_fields:
                    if hasattr(bso_config_obj, field_name):
                        optimizer_config[field_name] = getattr(bso_config_obj, field_name)
                    else:
                        # This case should ideally be caught by BatchSizeOptimizerConfig.__post_init__
                        missing_fields.append(field_name)

                if missing_fields:
                    error_msg = f"从 BatchSizeOptimizerConfig 对象读取时缺失字段: {', '.join(missing_fields)}"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)
                self.logger.info("从配置对象读取到批次大小优化器配置")

            elif isinstance(config, dict) and 'training' in config and isinstance(config['training'], dict) and 'batch_size_optimizer' in config['training']:
                bso_config_dict = config['training']['batch_size_optimizer']
                if isinstance(bso_config_dict, dict):
                    missing_fields = []
                    for field_name in required_fields:
                        if field_name in bso_config_dict:
                            optimizer_config[field_name] = bso_config_dict[field_name]
                        else:
                            missing_fields.append(field_name)
                    if missing_fields:
                        error_msg = f"从字典配置读取时批次大小优化器配置缺失必需字段: {', '.join(missing_fields)}"
                        self.logger.error(error_msg)
                        raise ValueError(error_msg)
                    self.logger.info("从字典配置读取到批次大小优化器配置")
                else:
                    error_msg = "training.batch_size_optimizer 配置必须是字典类型"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)
            else:
                error_msg = "未找到 training.batch_size_optimizer 配置"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
        except Exception as e:
            if isinstance(e, ValueError):
                raise
            error_msg = f"读取批次大小优化器配置失败: {e!s}"
            self.logger.error(error_msg)
            raise ValueError(error_msg) from e

        self._validate_optimizer_config_dict(optimizer_config) # Validate the constructed dict
        return optimizer_config

    def _validate_optimizer_config_dict(self, config_dict: dict[str, Any]) -> None:
        """验证从配置中读取的批次大小优化器配置字典"""
        # This method validates the dictionary *before* it's used to create BatchSizeOptimizerConfig
        # The BatchSizeOptimizerConfig.__post_init__ will validate the instance itself.
        # Duplicating all checks here might be redundant if __post_init__ is comprehensive.
        # However, for robustness, some basic checks on the dict can be useful.

        required_keys = [
            'enabled', 'initial_batch_size', 'min_batch_size', 'max_batch_size',
            'memory_utilization_target', 'strategy', 'adjustment_interval',
            'growth_factor', 'shrink_factor', 'stability_threshold',
            'warmup_steps', 'oom_recovery'
        ]
        for key in required_keys:
            if key not in config_dict:
                raise ValueError(f"读取的批次大小优化器配置字典中缺少键: {key}")

        if not isinstance(config_dict['enabled'], bool):
            raise ValueError(f"enabled 必须是布尔类型, 得到 {type(config_dict['enabled'])}")
        if not isinstance(config_dict['initial_batch_size'], int) or config_dict['initial_batch_size'] <= 0:
            raise ValueError(f"initial_batch_size 必须是正整数, 得到 {config_dict['initial_batch_size']}")
        # Add more specific validations for other fields if necessary,
        # though BatchSizeOptimizerConfig.__post_init__ should be the final authority.


def get_dynamic_batch_size_enabled(config: Any) -> bool:
    reader = BatchSizeOptimizerConfigReader()
    return reader.read_dynamic_batch_size_enabled(config)

def get_batch_size_optimizer_config(config: Any) -> dict[str, Any]:
    reader = BatchSizeOptimizerConfigReader()
    return reader.read_batch_size_optimizer_config(config)

def create_batch_size_optimizer_config(config: Any) -> BatchSizeOptimizerConfig:
    reader = BatchSizeOptimizerConfigReader()
    config_dict = reader.read_batch_size_optimizer_config(config)
    get_logger("create_batch_size_optimizer_config")

    noise_dim = None
    dimensions = None

    try:
        if hasattr(config, 'model'):
            noise_dim = config.model.noise_dim
            dimensions = config.model.dimensions
        elif isinstance(config, dict) and 'model' in config and isinstance(config['model'], dict):
            model_config = config['model']
            noise_dim = model_config['noise_dim']
            dimensions = model_config['dimensions']
        else:
            raise ValueError("未找到必需的 'model' 配置部分")

    except (AttributeError, KeyError, TypeError) as e:
        raise ValueError(f"获取必需的 model.noise_dim 或 model.dimensions 失败: {e!s}") from e

    if not isinstance(noise_dim, int):
        raise ValueError(f"noise_dim必须是整数类型，但获取到的是: {type(noise_dim)}")
    if not isinstance(dimensions, dict):
        raise ValueError(f"dimensions必须是字典类型，但获取到的是: {type(dimensions)}")

    return BatchSizeOptimizerConfig(
        enabled=config_dict['enabled'],
        initial_batch_size=config_dict['initial_batch_size'],
        min_batch_size=config_dict['min_batch_size'],
        max_batch_size=config_dict['max_batch_size'],
        memory_utilization_target=config_dict['memory_utilization_target'],
        strategy=config_dict['strategy'],
        adjustment_interval=config_dict['adjustment_interval'],
        growth_factor=config_dict['growth_factor'],
        shrink_factor=config_dict['shrink_factor'],
        stability_threshold=config_dict['stability_threshold'],
        warmup_steps=config_dict['warmup_steps'],
        oom_recovery=config_dict['oom_recovery'],
        noise_dim=noise_dim,
        dimensions=dimensions
    )

class BatchSizeOptimizer:
    """批次大小优化器

    根据系统资源状态动态调整批次大小，以最大化利用计算资源。
    支持多种调整策略：
    - memory: 基于GPU内存使用情况调整
    - performance: 基于训练性能指标调整
    - hybrid: 结合内存和性能指标调整
    """

    def __init__(
        self,
        config: BatchSizeOptimizerConfig,
        cuda_manager_instance: CUDAManager | None = None
    ):
        """初始化批次大小优化器

        Args:
            config: 优化器配置（必需）
            cuda_manager_instance: CUDA管理器实例，如果为None则使用全局实例
        """
        if config is None:
            raise ValueError("config参数不能为None，必须提供有效的BatchSizeOptimizerConfig实例")

        self.config = config # config is now an instance of BatchSizeOptimizerConfig
        self.logger = get_logger(self.__class__.__name__)
        self.cuda_manager = cuda_manager_instance or cuda_manager

        if self.cuda_manager is None:
            self.logger.warning("无法获取CUDA管理器，批次大小优化器将不会基于内存进行调整")

        # Access fields directly from self.config (BatchSizeOptimizerConfig instance)
        # __post_init__ in BatchSizeOptimizerConfig should have validated these.
        self.current_batch_size = self.config.initial_batch_size
        self.stable_count = 0
        self.step_count = 0
        self.last_adjustment_step = 0
        self.oom_count = 0
        self.performance_history = []
        self.memory_usage_history = []
        self.batch_size_history = []

        self.logger.info(
            f"批次大小优化器初始化:\n"
            f"- 初始批次大小: {self.current_batch_size}\n"
            f"- 最小批次大小: {self.config.min_batch_size}\n"
            f"- 最大批次大小: {self.config.max_batch_size}\n"
            f"- 目标内存利用率: {self.config.memory_utilization_target * 100:.0f}%\n"
            f"- 基于内存的批次大小调整"
        )

    def get_batch_size(self) -> int:
        """获取当前批次大小"""
        return int(self.current_batch_size)

    def step(
        self,
        loss: float | None = None,
        step_time: float | None = None,
        force_adjust: bool = False
    ) -> int:
        """执行优化步骤，根据内存利用率调整批次大小"""
        if not self.config.enabled:
            return int(self.current_batch_size)

        self.step_count += 1
        if self.cuda_manager:
            memory_info = self.cuda_manager.get_batch_peak_memory_info()
            if memory_info:
                self.memory_usage_history.append(memory_info.utilization)
                if not hasattr(self, '_last_logged_utilization') or \
                   abs(memory_info.utilization - getattr(self, '_last_logged_utilization', 0)) > 0.05:
                    self._last_logged_utilization = memory_info.utilization
                    self.logger.info(f"GPU批次内峰值内存利用率: {memory_info.utilization * 100:.1f}%")

        if (self.step_count - self.last_adjustment_step >= self.config.adjustment_interval) or force_adjust:
            self._adjust_by_memory()
            self.last_adjustment_step = self.step_count
        return int(self.current_batch_size)

    def _adjust_by_memory(self) -> None:
        """基于GPU内存使用情况调整批次大小"""
        if self.cuda_manager is None:
            self.logger.warning("CUDA管理器不可用，跳过基于内存的批次大小调整")
            return

        memory_info = self.cuda_manager.get_batch_peak_memory_info()
        if not memory_info:
            self.logger.warning("无法获取GPU内存信息，跳过批次大小调整")
            return

        current_utilization = memory_info.utilization
        target = self.config.memory_utilization_target

        self.logger.debug(
            f"GPU批次内峰值内存使用情况:\n"
            f"- 已用: {memory_info.used_gb:.2f} GB, 总计: {memory_info.total_gb:.2f} GB, 利用率: {current_utilization * 100:.1f}%\n"
            f"- 目标利用率: {target * 100:.1f}%, 当前批次大小: {self.current_batch_size}"
        )
        self.batch_size_history.append(self.current_batch_size)

        new_batch_size = self.current_batch_size
        adjustment_detail = "保持不变"

        if current_utilization < target * 0.75:
            increment = max(int(self.config.initial_batch_size * self.config.growth_factor * 0.1), 4) # Using growth_factor
            new_batch_size = min(self.current_batch_size + increment, self.config.max_batch_size)
            adjustment_detail = f"增加 (growth_factor: {self.config.growth_factor})"
        elif current_utilization > target * 1.02:
            decrement = max(int(self.config.initial_batch_size * self.config.shrink_factor * 0.1), 4) # Using shrink_factor
            new_batch_size = max(self.current_batch_size - decrement, self.config.min_batch_size)
            adjustment_detail = f"减少 (shrink_factor: {self.config.shrink_factor})"
        # ... (other adjustment logic can be added here if needed) ...

        new_batch_size = (new_batch_size // 2) * 2
        new_batch_size = max(new_batch_size, 2)

        if new_batch_size != self.current_batch_size:
            self.logger.info(
                f"基于内存利用率调整批次大小: {self.current_batch_size} -> {new_batch_size} ({adjustment_detail})"
            )
            self.current_batch_size = new_batch_size
            self.stable_count = 0
        else:
            self.stable_count += 1
            if self.stable_count > 0 and self.stable_count % self.config.stability_threshold == 0:
                self.logger.info(f"批次大小已稳定在 {self.current_batch_size} ({self.stable_count}次未调整)")


    def handle_oom(self) -> int:
        """处理内存溢出异常，减少批次大小"""
        if not self.config.oom_recovery:
            self.logger.warning("OOM恢复功能未启用，不调整批次大小")
            return int(self.current_batch_size)

        self.oom_count += 1
        decrement = int(self.current_batch_size * self.config.shrink_factor) # Use shrink_factor
        new_batch_size = max(self.current_batch_size - decrement, self.config.min_batch_size)
        new_batch_size = (new_batch_size // 2) * 2
        new_batch_size = max(new_batch_size, 2)

        self.logger.warning(
            f"检测到内存溢出(OOM) {self.oom_count} 次, 减少批次大小: {self.current_batch_size} -> {new_batch_size}"
        )
        if self.cuda_manager: self.cuda_manager.clear_cache()

        self.current_batch_size = new_batch_size
        self.stable_count = 0
        self.last_adjustment_step = self.step_count
        return int(self.current_batch_size)

    def reset(self) -> None:
        """重置优化器状态"""
        self.current_batch_size = self.config.initial_batch_size
        self.stable_count = 0
        self.step_count = 0
        self.last_adjustment_step = 0
        self.oom_count = 0
        self.performance_history = []
        self.memory_usage_history = []
        self.batch_size_history = []
        self.logger.info(f"批次大小优化器已重置，初始批次大小: {self.current_batch_size}")

    def get_stats(self) -> dict[str, Any]:
        """获取优化器统计信息"""
        return {
            "current_batch_size": self.current_batch_size,
            "initial_batch_size": self.config.initial_batch_size,
            "min_batch_size": self.config.min_batch_size,
            "max_batch_size": self.config.max_batch_size,
            "step_count": self.step_count,
            "stable_count": self.stable_count,
            "oom_count": self.oom_count,
            "batch_size_history": self.batch_size_history,
            "memory_usage_history": self.memory_usage_history
        }

_batch_size_optimizer_instance = None

def get_batch_size_optimizer(config: BatchSizeOptimizerConfig) -> BatchSizeOptimizer:
    global _batch_size_optimizer_instance
    if _batch_size_optimizer_instance is None:
        _batch_size_optimizer_instance = BatchSizeOptimizer(config)
    else:
        _batch_size_optimizer_instance.config = config
        _batch_size_optimizer_instance.reset()
    return _batch_size_optimizer_instance
