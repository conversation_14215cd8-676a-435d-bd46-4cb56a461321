import yaml
import json
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

def validate_config(config: Dict[str, Any]) -> None:
    """验证配置
    
    Args:
        config: 配置字典
    """
    # 基础配置验证
    if not isinstance(config, dict):
        raise ValueError("配置必须是字典类型")
        
    # 训练配置验证
    if "training" not in config:
        raise ValueError("配置中必须包含training字段")
        
    training_config = config["training"]
    if not isinstance(training_config, dict):
        raise ValueError("training配置必须是字典类型")
        
    # 验证mixed_precision配置
    if "mixed_precision" not in training_config:
        raise ValueError("配置中必须明确指定mixed_precision参数")
        
    mixed_precision = training_config["mixed_precision"]
    if not isinstance(mixed_precision, dict):
        raise ValueError("mixed_precision配置必须是字典类型")
        
    required_fields = ["enabled", "dtype", "loss_scale"]
    for field in required_fields:
        if field not in mixed_precision:
            raise ValueError(f"mixed_precision配置中必须包含{field}字段")

def load_config(config_path: str) -> Dict[str, Any]:
    """加载配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置字典
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            
        logger.error(f"加载配置文件 {config_path} 成功，完整配置内容: {json.dumps(config, ensure_ascii=False, indent=2)}")
        
        # 验证配置
        validate_config(config)
        
        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}")
        raise 