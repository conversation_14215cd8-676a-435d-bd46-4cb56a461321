"""优化数据过滤模块 - 提供参数探索模块使用的数据过滤功能"""

from __future__ import annotations

import pandas as pd

from src.optimization.exceptions import ConfigurationError
from src.utils.config import ConfigManager
from src.utils.logger import get_logger


def filter_data_for_optimization(
    data: pd.DataFrame,
    config: ConfigManager
) -> pd.DataFrame:
    """
    根据优化配置中的 optimization_start_date 参数过滤数据

    Args:
        data: 原始数据
        config: 配置管理器

    Returns:
        pd.DataFrame: 过滤后的数据

    Raises:
        ConfigurationError: 如果配置缺失或无效
    """
    logger = get_logger(__name__)

    # 检查配置
    if not hasattr(config, 'optimization') or config.optimization is None:
        raise ConfigurationError("配置中缺少 'optimization' 部分")

    # 获取优化起始日期
    opt_start_date_str = getattr(config.optimization, 'optimization_start_date', None)
    if not opt_start_date_str:
        raise ConfigurationError("配置 optimization.optimization_start_date 未设置或为空，参数探索需要此配置。")

    # 检查数据中是否有日期列
    date_col = 'date'
    if date_col not in data.columns:
        raise ConfigurationError(f"数据中缺少必需的日期列 '{date_col}'")

    # 确保日期列是 datetime 类型
    data[date_col] = pd.to_datetime(data[date_col])
    original_min_date = data[date_col].min()
    original_max_date = data[date_col].max()
    logger.info(f"原始数据时间范围: {original_min_date.date()} 到 {original_max_date.date()}")

    try:
        # 解析优化起始日期
        logger.info(f"检测到配置 optimization.optimization_start_date: '{opt_start_date_str}'")
        start_date = pd.to_datetime(opt_start_date_str)
        # 使用数据中的最大日期作为结束日期
        end_date = original_max_date if isinstance(original_max_date, pd.Timestamp) else pd.to_datetime(original_max_date)

        # 验证解析后的日期
        if pd.isna(start_date):
            raise ValueError("解析后的 start_date 为 NaT")
        if pd.isna(end_date):
            raise ValueError("原始数据的最大日期无效")
        if start_date > end_date:
            raise ValueError(f"起始日期 ({start_date.date()}) 不能晚于结束日期 ({end_date.date()})")

        # 应用过滤
        num_before = len(data)
        filtered_data = data[(data[date_col] >= start_date) & (data[date_col] <= end_date)].copy()
        num_after = len(filtered_data)

        if num_after == 0:
            raise ValueError(f"使用 optimization_start_date 过滤后没有剩余数据。范围: [{start_date.date()}] - [{end_date.date()}]")

        logger.info(f"已应用 optimization_start_date 过滤: [{start_date.date()}] - [{end_date.date()}]")
        logger.info(f"过滤前样本数: {num_before}, 过滤后样本数: {num_after}")

        return filtered_data

    except Exception as e:
        # 捕获所有在日期处理和过滤中发生的错误
        error_msg = f"处理 optimization.optimization_start_date ('{opt_start_date_str}') 或进行日期过滤时出错: {e}"
        logger.error(error_msg, exc_info=True)
        raise ConfigurationError(error_msg) from e
