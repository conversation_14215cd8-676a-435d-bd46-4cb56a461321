"""测试判别器参数量变化

此脚本用于测试判别器参数量的变化，验证精简后的参数量减少是否达到预期。
"""

import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.abspath('.'))

from src.models.gan.discriminator import TimeSeriesDiscriminator


def count_parameters(model):
    """计算模型参数量"""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

def main():
    """主函数"""
    # 使用模拟配置
    from unittest.mock import MagicMock
    config = MagicMock()

    # 创建判别器
    target_dim = 1
    condition_feature_dim = 240  # 假设特征维度为240
    hidden_dim = 128  # 与config_best.yaml中的设置一致

    # 初始化判别器
    discriminator = TimeSeriesDiscriminator(
        target_dim=target_dim,
        condition_feature_dim=condition_feature_dim,
        hidden_dim=hidden_dim,
        config=config
    )

    # 计算参数量
    total_params = count_parameters(discriminator)

    # 打印参数量
    print(f"\n判别器参数量: {total_params:,}")

    # 打印各分支参数量
    if hasattr(discriminator, 'trend_branch') and discriminator.trend_branch is not None:
        trend_params = count_parameters(discriminator.trend_branch)
        print(f"趋势一致性分支参数量: {trend_params:,} ({trend_params/total_params*100:.2f}%)")

    if hasattr(discriminator, 'feature_branch') and discriminator.feature_branch is not None:
        feature_params = count_parameters(discriminator.feature_branch)
        print(f"特征相关性分支参数量: {feature_params:,} ({feature_params/total_params*100:.2f}%)")

    if hasattr(discriminator, 'temporal_branch') and discriminator.temporal_branch is not None:
        temporal_params = count_parameters(discriminator.temporal_branch)
        print(f"时序模式分支参数量: {temporal_params:,} ({temporal_params/total_params*100:.2f}%)")

    # 打印其他组件参数量
    other_params = total_params
    if hasattr(discriminator, 'trend_branch') and discriminator.trend_branch is not None:
        other_params -= count_parameters(discriminator.trend_branch)
    if hasattr(discriminator, 'feature_branch') and discriminator.feature_branch is not None:
        other_params -= count_parameters(discriminator.feature_branch)
    if hasattr(discriminator, 'temporal_branch') and discriminator.temporal_branch is not None:
        other_params -= count_parameters(discriminator.temporal_branch)

    print(f"其他组件参数量: {other_params:,} ({other_params/total_params*100:.2f}%)")

    # 打印注意力机制参数量
    if hasattr(discriminator, 'temporal_attention') and discriminator.temporal_attention is not None:
        temporal_attn_params = count_parameters(discriminator.temporal_attention)
        print(f"时序注意力参数量: {temporal_attn_params:,} ({temporal_attn_params/total_params*100:.2f}%)")

    if hasattr(discriminator, 'multi_scale_attention') and discriminator.multi_scale_attention is not None:
        multi_scale_params = count_parameters(discriminator.multi_scale_attention)
        print(f"多尺度注意力参数量: {multi_scale_params:,} ({multi_scale_params/total_params*100:.2f}%)")

    if hasattr(discriminator, 'adaptive_attention') and discriminator.adaptive_attention is not None:
        adaptive_params = count_parameters(discriminator.adaptive_attention)
        print(f"自适应注意力参数量: {adaptive_params:,} ({adaptive_params/total_params*100:.2f}%)")

if __name__ == "__main__":
    main()
