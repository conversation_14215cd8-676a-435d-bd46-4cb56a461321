"""判别器模块 - 提供GAN网络的时序数据评估框架"""

from __future__ import annotations

import torch
from torch import nn

from src.models.base.base_module import BaseModule
from src.utils.config.model import (
    DiscriminatorConfig,
    GANModelConfig,
)
from src.utils.config_manager import ConfigManager
from src.utils.cuda import cuda_manager

from .attention import MultiScaleAttention
from .components.attention_factory import create_adaptive_attention
from .components.temporal import TemporalMultiHeadWrapper
from .discriminator_branches import (
    FeatureCorrelationBranch,
    TemporalPatternBranch,
    TrendConsistencyBranch,
)
from .dynamic_feature_fusion import DynamicFeatureFusion


class TimeSeriesDiscriminator(BaseModule):
    """多维度判别器实现

    包含三个判别维度：
    1. 趋势一致性判别
    2. 特征关联判别
    3. 时序模式判别
    """

    def _compute_attention(
        self,
        features_norm: torch.Tensor,
        attention_module: nn.Module
    ) -> tuple[torch.Tensor, torch.Tensor]:
        """计算注意力机制的核心逻辑

        Args:
            features_norm: 规范化后的特征
            attention_module: 注意力模块

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 注意力输出和注意力权重
        """
        # 使用更小的缩放因子进一步降低数值范围
        scale_factor_attn = 1024.0  # 增大缩放因子
        query = features_norm / scale_factor_attn
        key = features_norm / scale_factor_attn
        value = features_norm / scale_factor_attn


        # 直接调用注意力模块，不捕获异常
        # 开发阶段，任何异常都应该立即抛出，以便快速定位和解决问题
        attention_out, attention_weights = attention_module(query, key, value)

        # 检查注意力输出是否包含NaN/Inf
        if torch.isnan(attention_out).any() or torch.isinf(attention_out).any():
            self.logger.error("注意力输出包含NaN/Inf，这是一个严重错误")
            raise RuntimeError("注意力输出包含NaN/Inf，需要检查注意力机制的实现")

        # 对输出进行缩放
        attention_out = attention_out * scale_factor_attn

        return attention_out, attention_weights

    # 判别分支已移至discriminator_branches.py文件

    def __init__(self, target_dim: int, condition_feature_dim: int | None, hidden_dim: int, config: ConfigManager):
        super().__init__("TimeSeriesDiscriminator")
        self.config = config # 将 config 赋值提前

        # Initialize attributes to None early to prevent AttributeError
        self.temporal_attention = None # Replaced hierarchical_attention
        self.multi_scale_attention = None
        self.adaptive_attention = None # 添加自适应扩张率注意力机制
        self.trend_branch = None
        self.feature_branch = None
        self.temporal_branch = None
        self.current_condition_feature_dim = None # Initialize here
        self.total_input_dim = None
        self.dynamic_fusion = None # 添加 dynamic_fusion 占位符

        # 注意力机制启用开关 (从配置中读取，严格要求配置存在)

        # 确保 self.config.model 是 GANModelConfig 类型
        if not isinstance(self.config.model, GANModelConfig):
            raise TypeError(
                f"期望 self.config.model 是 GANModelConfig 类型以访问判别器配置, "
                f"但得到了 {type(self.config.model)}"
            )
        gan_model_cfg: GANModelConfig = self.config.model

        # 首先获取判别器的配置字典
        raw_discriminator_config_data = gan_model_cfg.discriminator
        if not isinstance(raw_discriminator_config_data, dict):
            # 这个检查理论上不应该触发，因为 GANModelConfig.discriminator 注解为 Dict[str, Any]
            # 但作为防御性编程添加
            raise TypeError(f"期望 gan_model_cfg.discriminator 是一个字典, 但得到了 {type(raw_discriminator_config_data)}")

        # 创建一个可修改的副本，用于后续可能的填充
        discriminator_config_data = raw_discriminator_config_data.copy()

        # 根据用户的指示，input_features 是动态传递的，不应在 config.yaml 中提前设置。
        # DiscriminatorConfig 中的 input_features 字段已被设为 Optional[int] = None。
        # 如果 condition_feature_dim 在此已知，则计算并填充 input_features。
        # 否则，它将保持为 None，这对于 DiscriminatorConfig 现在是可接受的。
        if 'input_features' not in discriminator_config_data: # 仅当配置中未提供时才动态计算
            if condition_feature_dim is not None:
                # target_dim 在 __init__ 中是已知的 (值为1)
                calculated_input_features = condition_feature_dim + target_dim
                discriminator_config_data['input_features'] = calculated_input_features
                self.logger.info(f"动态计算并设置判别器 input_features: {calculated_input_features} (基于 condition_feature_dim: {condition_feature_dim} 和 target_dim: {target_dim})")
            else:
                self.logger.info("condition_feature_dim 为 None 且 config.yaml 中未提供 input_features，DiscriminatorConfig 中的 input_features 将为 None。")
        else:
            self.logger.info(f"使用来自 config.yaml 的判别器 input_features: {discriminator_config_data['input_features']}")

        # 根据用户反馈，learning_rate, beta1, beta2, hidden_dims 等参数
        # 应该在 config.yaml 的 model.discriminator 部分明确定义，此处不再从其他地方填充。
        # 如果这些参数在 config.yaml 中缺失，DiscriminatorConfig 的 __post_init__ 会报错。

        # 使用字典数据创建 DiscriminatorConfig 实例
        # DiscriminatorConfig 的 __post_init__ 会检查所有必需字段是否存在
        try:
            actual_discriminator_config = DiscriminatorConfig(**discriminator_config_data)
        except Exception as e:
            self.logger.error(f"从字典创建 DiscriminatorConfig 实例失败。字典数据: {discriminator_config_data}。错误: {e}")
            # 根据项目策略，这里可以抛出异常或设置默认值（但用户要求严格配置）
            raise ValueError(f"无法为判别器创建有效的配置对象: {e}") from e

        self.enable_dynamic_fusion = actual_discriminator_config.enable_dynamic_fusion
        self.enable_temporal_attention = actual_discriminator_config.enable_temporal_attention
        self.enable_adaptive_dilation_attention = actual_discriminator_config.enable_adaptive_dilation_attention
        self.enable_multiscale_convolution_attention = actual_discriminator_config.enable_multiscale_convolution_attention

        self.target_dim = target_dim
        self.hidden_dim = hidden_dim
        # self.config = config # 已提前赋值
        self.dropout_rate = gan_model_cfg.dropout_rate # 明确读取 dropout_rate

        # 初始化特征维度
        # self.current_condition_feature_dim = None # Moved earlier
        self._init_feature_dim(condition_feature_dim)

        # 初始化三个判别分支
        self._init_branches()

        # Clean up comments left from previous partial apply
        # 增强的动态权重学习网络 (输入维度不变，因为它处理的是分支的输出分数)
        self.weight_net = nn.Sequential(
            nn.Linear(3, 128),
            nn.LayerNorm(128),
            nn.GELU(),
            nn.Dropout(self.dropout_rate), # 使用配置的 dropout_rate
            nn.Linear(128, 3),
            nn.Softmax(dim=-1)
        )

        # 配置参数初始化
        # 从模型配置中获取混合精度设置
        mixed_precision_config = getattr(config.training, 'mixed_precision', None)
        self.use_amp = False
        if mixed_precision_config is not None:
            self.use_amp = getattr(mixed_precision_config, 'enabled', False)
        if self.use_amp:
            self.logger.info("混合精度训练已启用")
        self.device = cuda_manager.device

        # 参数初始化
        self._init_weights()

        # 日志记录
        if self.current_condition_feature_dim is not None:
            total_input_dim = self.target_dim + self.current_condition_feature_dim
            self.logger.info(f"初始化多维度判别器 | 目标维度:{target_dim} 条件维度:{condition_feature_dim} 总输入维度:{total_input_dim} 隐藏维度:{hidden_dim}")
        else:
            self.logger.info(f"初始化多维度判别器 | 目标维度:{target_dim} 条件维度:未知(将动态确定) 隐藏维度:{hidden_dim}")
        self.logger.debug(f"混合精度模式: {'启用' if self.use_amp else '禁用'}")
        self.logger.debug(f"设备分配: {self.device}")

    def _init_weights(self, module=None):
        """使用Kaiming初始化权重

        Args:
            module: 要初始化的模块 (可选)，如果为None则初始化所有模块
        """
        modules = [module] if module is not None else self.modules()
        for mod in modules:
            if isinstance(mod, nn.Linear):
                nn.init.xavier_normal_(mod.weight, gain=nn.init.calculate_gain('relu'))
                if mod.bias is not None:
                    nn.init.constant_(mod.bias, 0)

    def _init_feature_dim(self, condition_feature_dim: int | None = None):
        """初始化特征维度

        Args:
            condition_feature_dim: 条件特征维度，可以为None表示动态确定
        """
        # This logic remains, but the attribute self.current_condition_feature_dim is initialized earlier
        if condition_feature_dim is not None:
            if condition_feature_dim <= 0:
                raise ValueError(f"无效的特征维度: {condition_feature_dim}")
            self.current_condition_feature_dim = condition_feature_dim
        # else: # No need for else, it's already None
            # self.logger.info("特征维度未指定，将在第一次前向传播时动态确定")
            # self.current_condition_feature_dim = None # Already initialized to None

    def _init_branches(self):
        """初始化判别分支"""
        # 确保 self.config.model 是 GANModelConfig 类型，以便后续访问 attention 属性
        if not isinstance(self.config.model, GANModelConfig):
            # 这个检查理论上不应该触发，因为 __init__ 中已经有类似的检查
            # 但作为防御性编程和明确类型提示，再次添加
            raise TypeError(
                f"期望 self.config.model 是 GANModelConfig 类型以访问注意力配置, "
                f"但得到了 {type(self.config.model)}"
            )
        gan_model_cfg: GANModelConfig = self.config.model # 在此方法作用域内定义 gan_model_cfg


        if self.current_condition_feature_dim is None:
            # 如果特征维度未知，延迟初始化 (Attributes are already None)
            self.logger.info("特征维度未知，延迟初始化判别分支")
            # self.trend_branch = None # Already initialized
            # self.feature_branch = None # Already initialized
            # self.temporal_branch = None # Already initialized
            # self.total_input_dim = None # Already initialized
            return

        total_input_dim = self.target_dim + self.current_condition_feature_dim
        self.total_input_dim = total_input_dim

        # 初始化三个判别分支，并传递 dropout_rate
        self.trend_branch = TrendConsistencyBranch(total_input_dim, self.hidden_dim, dropout_rate=self.dropout_rate)
        self.feature_branch = FeatureCorrelationBranch(total_input_dim, dropout_rate=self.dropout_rate)
        self.temporal_branch = TemporalPatternBranch(total_input_dim, dropout_rate=self.dropout_rate)

        # Initialize TemporalMultiHeadWrapper here as total_input_dim is known
        if self.enable_temporal_attention:
            if self.temporal_attention is None: # Initialize only if not already done
    # 断言检查：确保 n_heads 配置存在且不为 None (规则 #4, #9)
                assert gan_model_cfg.attention.multi_head_num_heads is not None, \
                    "配置错误：模型注意力配置中缺少 'multi_head_num_heads' (model.attention.multi_head_num_heads)。请在 config.yaml 中设置此值。"
                assert gan_model_cfg.attention.temporal_wrapper_dropout is not None, \
                    "配置错误：模型注意力配置中缺少 'temporal_wrapper_dropout' (model.attention.temporal_wrapper_dropout)。请在 config.yaml 中设置此值。"
                self.temporal_attention = TemporalMultiHeadWrapper(
                    hidden_dim=total_input_dim, # Use the actual input dimension
                    num_heads=gan_model_cfg.attention.multi_head_num_heads, # 从 attention config 获取
                    dropout=gan_model_cfg.attention.temporal_wrapper_dropout # 从 attention config 获取
                ).to(self.device)
                self.logger.info(f"初始化 TemporalMultiHeadWrapper - hidden_dim: {total_input_dim}, num_heads: {gan_model_cfg.attention.multi_head_num_heads}, dropout: {gan_model_cfg.attention.temporal_wrapper_dropout}")
        else:
            self.temporal_attention = None
            self.logger.info("TemporalMultiHeadWrapper 已被禁用通过配置。")

        # Initialize MultiScaleAttention here as total_input_dim is known
        if self.enable_multiscale_convolution_attention:
            if self.multi_scale_attention is None: # Initialize only if not already done
                # gan_model_cfg 已经在此方法开头定义和类型提示
                attention_cfg = gan_model_cfg.attention

                self.multi_scale_attention = MultiScaleAttention(
                    embed_dim=total_input_dim, # Use the actual input dimension it will receive
                    num_heads=attention_cfg.multi_scale_num_heads,
                    num_scales=attention_cfg.multi_scale_num_scales,
                    dilation_rates=attention_cfg.multi_scale_dilation_rates,
                    dropout=attention_cfg.multi_scale_dropout
                ).to(self.device)
                self.logger.info(f"初始化 MultiScaleAttention (convolution-based) - embed_dim: {total_input_dim}, "
                                 f"num_heads: {attention_cfg.multi_scale_num_heads}, "
                                 f"num_scales: {attention_cfg.multi_scale_num_scales}, "
                                 f"dilation_rates: {attention_cfg.multi_scale_dilation_rates}, "
                                 f"dropout: {attention_cfg.multi_scale_dropout}")
        else:
            self.multi_scale_attention = None
            self.logger.info("MultiScaleAttention (convolution-based) 已被禁用通过配置。")

        # 初始化自适应扩张率注意力机制
        if self.enable_adaptive_dilation_attention:
            if self.adaptive_attention is None: # 仅在尚未初始化时初始化
                # 创建CUDA流配置
                cuda_stream_config = {
                    "max_batch_streams": 8,
                    "max_scale_streams": 4,
                    "verbose_logging": False
                }
                self.logger.info(f"创建CUDA流配置: {cuda_stream_config}")

                # 再次断言 attention 配置中的相关字段不是 None
                assert gan_model_cfg.attention.adaptive_attention_num_heads is not None, \
                    "配置错误：model.attention.adaptive_attention_num_heads 在初始化自适应注意力时为 None。"
                assert gan_model_cfg.attention.adaptive_attention_num_scales is not None, \
                    "配置错误：model.attention.adaptive_attention_num_scales 在初始化自适应注意力时为 None。"
                assert gan_model_cfg.attention.adaptive_attention_dropout is not None, \
                    "配置错误：model.attention.adaptive_attention_dropout 在初始化自适应注意力时为 None。"

                adaptive_num_heads = gan_model_cfg.attention.adaptive_attention_num_heads
                adaptive_num_scales = gan_model_cfg.attention.adaptive_attention_num_scales
                adaptive_dropout = gan_model_cfg.attention.adaptive_attention_dropout

                self.adaptive_attention = create_adaptive_attention(
                    embed_dim=total_input_dim, # 使用实际输入维度
                    num_heads=adaptive_num_heads, # 从 attention config 获取
                    num_scales=adaptive_num_scales, # 从 attention config 获取
                    dropout=adaptive_dropout, # 从 attention config 获取
                    use_cuda_streams=True, # 启用CUDA流
                    cuda_stream_config=cuda_stream_config, # 添加CUDA流配置
                    device=self.device
                )
                self.logger.info(f"初始化自适应扩张率注意力机制 - embed_dim: {total_input_dim}, num_heads: {adaptive_num_heads}, num_scales: {adaptive_num_scales}, dropout: {adaptive_dropout}")
        else:
            self.adaptive_attention = None
            self.logger.info("AdaptiveDilationAttention 已被禁用通过配置。")

        # 初始化动态特征融合模块 (使用已知的维度)
        if self.enable_dynamic_fusion:
            if self.dynamic_fusion is None:
                # 使用目标维度作为hidden_dim
                self.dynamic_fusion = DynamicFeatureFusion(
                    feature_dim=self.target_dim, # 目标序列维度
                    context_dim=self.current_condition_feature_dim, # 条件特征维度
                    hidden_dim=self.target_dim # 使用目标维度作为隐藏层维度
                ).to(self.device)
                self.logger.info(f"初始化 DynamicFeatureFusion - feature_dim: {self.target_dim}, context_dim: {self.current_condition_feature_dim}, hidden_dim: {self.target_dim}")
        else:
            self.dynamic_fusion = None
            self.logger.info("DynamicFeatureFusion 已被禁用通过配置。")

    def _update_feature_dim(self, new_condition_feature_dim: int):
        """动态更新特征维度

        Args:
            new_condition_feature_dim: 新条件特征维度
        """
        if new_condition_feature_dim == self.current_condition_feature_dim:
            return

        if new_condition_feature_dim <= 0:
            raise ValueError(f"无效的特征维度: {new_condition_feature_dim}")

        # 如果当前特征维度为None，表示首次初始化
        if self.current_condition_feature_dim is None:
            self.logger.info(f"首次初始化判别器特征维度: {new_condition_feature_dim}")
            self.current_condition_feature_dim = new_condition_feature_dim
            self._init_branches()
            return

        self.logger.info(f"更新判别器特征维度: {self.current_condition_feature_dim} -> {new_condition_feature_dim}")

        # 更新特征维度并重新初始化分支
        self.current_condition_feature_dim = new_condition_feature_dim
        self._init_branches()

    def forward(self, target_sequence: torch.Tensor, condition_features: torch.Tensor, data_source: str = "Unknown") -> torch.Tensor:
        """判别器前向传播

        Args:
            target_sequence: 目标序列 [batch_size, seq_len, target_dim] (target_dim=1)
            condition_features: 条件特征 [batch_size, seq_len, condition_feature_dim]
            data_source: 数据来源标识 ("Real" 或 "Fake")，仅用于日志记录

        Returns:
            torch.Tensor: 判别概率 [batch_size, 1]
        """
        # 注意：data_source参数在注释掉的_validate_intermediate_output调用中使用
        # 保留此参数以保持API兼容性

        # 检查并更新特征维度
        actual_feature_dim = condition_features.size(2)
        if actual_feature_dim != self.current_condition_feature_dim:
            self._update_feature_dim(actual_feature_dim)

        # HierarchicalAttention initialization removed, assuming temporal_attention is initialized in _init_branches/_update_feature_dim

        # 验证输入形状
        assert target_sequence.dim() == 3, f"目标序列维度应为3，实际为 {target_sequence.dim()}"
        assert condition_features.dim() == 3, f"条件特征维度应为3，实际为 {condition_features.dim()}"
        assert target_sequence.size(0) == condition_features.size(0), "批次大小不匹配"
        assert target_sequence.size(1) == condition_features.size(1), "序列长度不匹配"

        # 验证输入维度 (规则 #70)
        assert target_sequence.size(2) == 1, f"目标序列最后一维应为1，实际为 {target_sequence.size(2)}"
        assert condition_features.size(2) >= 1, f"条件特征维度应≥1，实际为 {condition_features.size(2)}"
        self.logger.debug(f"_DEBUG: 输入维度验证通过 - target:{target_sequence.shape}, condition:{condition_features.shape}")

        # === 添加日志记录以检查输入形状 (遵循规则 #45a) ===
        self.logger.debug(f"_DEBUG: Discriminator input shapes - target_sequence: {target_sequence.shape}, condition_features: {condition_features.shape}")
        # ==================================

        # 应用动态特征融合 (使用条件特征作为上下文，加权目标序列)
        if self.enable_dynamic_fusion and self.dynamic_fusion is not None:
            fused_target_sequence = self.dynamic_fusion(features=target_sequence, context=condition_features)
        else:
            fused_target_sequence = target_sequence # 如果禁用，直接使用原始目标序列
            self.logger.debug("DynamicFeatureFusion 已跳过 (根据配置)。")

        # 检查数值稳定性
        self._check_numerical_stability(fused_target_sequence, "Fused Target Sequence")

        # 拼接融合后的目标序列和条件特征
        # [B, seq_len, target_dim] + [B, seq_len, condition_feature_dim] -> [B, seq_len, total_input_dim]
        # 强制转换为 float32 进行后续计算
        combined_input = torch.cat([fused_target_sequence.float(), condition_features.float()], dim=2)

        # 检查拼接结果的数值稳定性
        self._check_numerical_stability(combined_input, "Combined Input")
        if data_source not in ["Real", "Fake", "Unknown"]:
            self.logger.warning(f"未知的数据来源标识: {data_source}")
        # self._validate_intermediate_output(combined_input, "Combined Input", data_source=data_source) # 注释掉以减少日志

        # 检查判别分支是否初始化
        if self.trend_branch is None or self.feature_branch is None or self.temporal_branch is None:
            self.logger.error("判别分支未初始化，这不应该发生，因为已经调用了_update_feature_dim")
            # 强制初始化
            self._init_branches()

        # 再次检查判别分支是否初始化
        if self.trend_branch is None or self.feature_branch is None or self.temporal_branch is None:
            raise RuntimeError("判别分支初始化失败")

        # 禁用CuDNN后端以避免double backward问题
        # 强制 float32 计算分支
        with torch.backends.cudnn.flags(enabled=False):
            # --- 注意力计算移至此处以强制 float32 ---
            # 添加更详细的输入日志
            current_features = combined_input.float()

            # 检查输入数值稳定性
            self._check_numerical_stability(current_features, "Temporal Attention Input")

            # 应用 TemporalMultiHeadWrapper (强制 float32)
            if self.enable_temporal_attention and self.temporal_attention is not None:
                self.logger.debug(f"TemporalMultiHeadWrapper Input Stats: "
                                  f"min={current_features.min().item():.4e}, "
                                  f"max={current_features.max().item():.4e}, "
                                  f"mean={current_features.mean().item():.4e}, "
                                  f"std={current_features.std().item():.4e}, "
                                  f"has_nan={torch.isnan(current_features).any()}, "
                                  f"has_inf={torch.isinf(current_features).any()}")
                features_temporal_out, _ = self.temporal_attention(current_features)
                # 检查输出数值稳定性
                self._check_numerical_stability(features_temporal_out, "Temporal Attention Output")
                current_features = features_temporal_out
            else:
                self.logger.debug("TemporalMultiHeadWrapper 已跳过 (根据配置)。")

            # 应用自适应扩张率注意力机制
            if self.enable_adaptive_dilation_attention and self.adaptive_attention is not None:
                # 检查输入数值稳定性
                self._check_numerical_stability(current_features, "Adaptive Attention Input")
                features_adaptive_out, _ = self.adaptive_attention(current_features)
                # 检查输出数值稳定性
                self._check_numerical_stability(features_adaptive_out, "Adaptive Attention Output")
                current_features = features_adaptive_out
            else:
                self.logger.debug("AdaptiveDilationAttention 已跳过 (根据配置)。")

            # 应用多尺度注意力
            if self.enable_multiscale_convolution_attention and self.multi_scale_attention is not None:
                # 检查输入数值稳定性
                self._check_numerical_stability(current_features, "MultiScale Attention Input")
                features_multi_scale_out = self.multi_scale_attention(current_features)
                # 检查输出数值稳定性
                self._check_numerical_stability(features_multi_scale_out, "MultiScale Attention Output")
                current_features = features_multi_scale_out
            else:
                self.logger.debug("MultiScaleAttention (convolution-based) 已跳过 (根据配置)。")

            # --- 注意力计算结束 ---
            # 最终的 current_features 将送入判别分支
            features_multi_scale = current_features # 重命名以便后续代码兼容

            # 序列化计算三个分支，避免同时占用显存

            # 1. 趋势一致性判别
            # 输入需要是 [B, total_input_dim, seq_len]
            # 创建趋势输入变量
            trend_input = features_multi_scale.transpose(1, 2).float()

            # 检查输入数值稳定性
            self._check_numerical_stability(trend_input, "Trend Branch Input")
            trend_score = self.trend_branch(trend_input)
            # 检查输出数值稳定性
            self._check_numerical_stability(trend_score, "Trend Branch Output")

            # 立即释放不再需要的中间结果
            del trend_input
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            # 2. 特征关联判别
            # 输入是 [B, seq_len, total_input_dim]
            # 创建特征输入变量
            feature_input = features_multi_scale.float()

            # 检查输入数值稳定性
            self._check_numerical_stability(feature_input, "Feature Branch Input")
            feature_score = self.feature_branch(feature_input)
            # 检查输出数值稳定性
            self._check_numerical_stability(feature_score, "Feature Branch Output")

            # 立即释放不再需要的中间结果
            del feature_input
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            # 3. 时序模式判别
            # 输入是 [B, seq_len, total_input_dim]
            # 创建时序输入变量
            temporal_input = features_multi_scale.float()

            # 检查输入数值稳定性
            self._check_numerical_stability(temporal_input, "Temporal Branch Input")
            temporal_score = self.temporal_branch(temporal_input)
            # 检查输出数值稳定性
            self._check_numerical_stability(temporal_score, "Temporal Branch Output")

            # 立即释放不再需要的中间结果
            del temporal_input
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            # 拼接各维度得分
            combined_scores = torch.cat([
                trend_score.unsqueeze(-1),
                feature_score.unsqueeze(-1),
                temporal_score.unsqueeze(-1)
            ], dim=-1).float()  # [B, 3], 确保是 float32
            # 检查拼接结果的数值稳定性
            self._check_numerical_stability(combined_scores, "Combined Branch Scores")
            # self._validate_intermediate_output(combined_scores, "Combined Branch Scores", data_source=data_source) # 注释掉以减少日志

            # 动态权重融合
            weights = self.weight_net(combined_scores.float())  # [B, 3]
            # 检查权重数值稳定性
            self._check_numerical_stability(weights, "Fusion Weights")
            # self._validate_intermediate_output(weights, "Fusion Weights", data_source=data_source) # 注释掉以减少日志

            # 加权平均
            final_score = (weights * combined_scores).sum(dim=-1, keepdim=True).float()
            # 检查最终得分数值稳定性
            self._check_numerical_stability(final_score, "Final Score")
            # self._validate_intermediate_output(final_score, "Final Score", data_source=data_source) # 注释掉以减少日志

        return final_score

    def extract_features(self, target_sequence: torch.Tensor, condition_features: torch.Tensor | None = None) -> list[torch.Tensor]:
        """提取特征

        Args:
            target_sequence: 目标序列
            condition_features: 条件特征（可选）

        Returns:
            List[torch.Tensor]: 特征列表
        """
        features = []

        # 检查是否提供了条件特征
        if condition_features is None:
            raise ValueError("条件特征 (condition_features) 必须提供给 extract_features 方法。")

        # 拼接目标序列和条件特征
        if self.enable_dynamic_fusion and self.dynamic_fusion is not None:
            fused_target_sequence_ext = self.dynamic_fusion(features=target_sequence, context=condition_features)
        else:
            fused_target_sequence_ext = target_sequence

        combined_features = torch.cat([fused_target_sequence_ext, condition_features], dim=2)
        features.append(combined_features)

        current_features_ext = combined_features

        # 应用 TemporalMultiHeadWrapper
        if self.enable_temporal_attention and self.temporal_attention is not None:
            temporal_features_out, _ = self.temporal_attention(current_features_ext)
            features.append(temporal_features_out)
            current_features_ext = temporal_features_out
        else:
            self.logger.debug("extract_features: TemporalMultiHeadWrapper 已跳过 (根据配置)。")
            # features.append(current_features_ext) # Optionally append the input if module is skipped

        # 应用自适应扩张率注意力机制
        if self.enable_adaptive_dilation_attention and self.adaptive_attention is not None:
            adaptive_features_out, _ = self.adaptive_attention(current_features_ext)
            features.append(adaptive_features_out)
            current_features_ext = adaptive_features_out
        else:
            self.logger.debug("extract_features: AdaptiveDilationAttention 已跳过 (根据配置)。")
            # features.append(current_features_ext)

        # 应用多尺度注意力
        if self.enable_multiscale_convolution_attention and self.multi_scale_attention is not None:
            multi_scale_features_out = self.multi_scale_attention(current_features_ext) # 使用自适应注意力的输出或更早的输出
            features.append(multi_scale_features_out)
            # current_features_ext = multi_scale_features_out # Last in chain for feature extraction
        else:
            self.logger.debug("extract_features: MultiScaleAttention (convolution-based) 已跳过 (根据配置)。")
            # features.append(current_features_ext)

        # 应用趋势一致性分支
        if self.trend_branch is not None:
            trend_features = self.trend_branch.extract_features(combined_features.transpose(1, 2))
            features.append(trend_features)

        # 应用特征关联分支
        if self.feature_branch is not None:
            correlation_features = self.feature_branch.extract_features(combined_features)
            features.append(correlation_features)

        # 应用时序模式分支
        if self.temporal_branch is not None:
            temporal_features = self.temporal_branch.extract_features(combined_features)
            features.append(temporal_features)

        return features

    def _validate_intermediate_output(
        self,
        tensor: torch.Tensor,
        name: str,
        check_grad: bool = False,
        max_abs_value: float = 1e3,
        data_source: str = "Unknown" # Add data_source parameter
    ) -> None:
        """验证中间输出的有效性，包含完整的断言检查链和错误码

        Args:
            tensor: 要验证的张量
            name: 张量名称（用于日志）
            check_grad: 是否检查梯度
            max_abs_value: 最大允许的绝对值

        Raises:
            ValueError: 当验证失败时，包含错误码
        """
        try:
            # Include data_source in the log message
            self.logger.debug(
                f"\n开始验证 [{name}] (数据来源: {data_source}):\n"
                f"1. 输入信息:\n"
                f"   - 形状: {tensor.shape}\n"
                f"   - 设备: {tensor.device}\n"
                f"   - 类型: {tensor.dtype}\n"
                f"2. 当前状态:\n"
                f"   - 目标设备: {self.device}\n"
                f"   - 混合精度: {'启用' if self.use_amp else '禁用'}"
            )

            tensor_float32 = tensor.to(torch.float32) if tensor.dtype != torch.float32 else tensor

            if torch.isnan(tensor_float32).any():
                raise ValueError(f"{name}包含NaN值 (错误码:D001)")

            if torch.isinf(tensor_float32).any():
                raise ValueError(f"{name}包含Inf值 (错误码:D002)")

            abs_max = tensor_float32.abs().max().item()
            if abs_max > max_abs_value:
                raise ValueError(
                    f"{name}数值超出预期范围 (错误码:D003):\n"
                    f"- 最大绝对值: {abs_max:.3e}\n"
                    f"- 允许范围: [-{max_abs_value:.3e}, {max_abs_value:.3e}]"
                )

            if tensor.device != self.device:
                raise ValueError(
                    f"{name}设备不匹配 (错误码:D007):\n"
                    f"- 当前设备: {tensor.device}\n"
                    f"- 期望设备: {self.device}"
                )

            if check_grad and tensor.requires_grad:
                try:
                    # 检查梯度
                    with torch.no_grad():
                        probe = torch.sum(tensor_float32)
                        if torch.isnan(probe) or torch.isinf(probe):
                            raise ValueError(f"{name}的probe值无效 (错误码:D008)")

                except Exception as e:
                    self.logger.warning(f"梯度检查失败: {e!s}")

        except ValueError as e_val:
            raise e_val from None # 修改：重新抛出捕获的ValueError，并显式处理异常链
        except Exception as e:
            raise ValueError(f"{name}验证失败 (错误码:D099): {e!s}") from e # 修改：链接原始异常

    def _check_numerical_stability(self, tensor: torch.Tensor, name: str, fix_tensor: bool = False) -> None:
        """[内部使用] 快速数值稳定性检查
        仅用于判别器前向传播中的轻量级验证

        Args:
            tensor: 要检查的张量
            name: 张量标识名(用于日志)
            fix_tensor: 已废弃参数，保留仅为兼容性

        Raises:
            ValueError: 当检测到NaN或Inf值时
        """
        has_nan = torch.isnan(tensor).any()
        has_inf = torch.isinf(tensor).any()

        if has_nan or has_inf:
            error_msg = f"{name} 包含{'NaN' if has_nan else ''}{'和' if has_nan and has_inf else ''}{'Inf' if has_inf else ''}值"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
