"""模型状态管理模块 - 提供统一的模型状态跟踪与管理框架

项目结构模块索引：
1. 基础设施模块:
   - src/utils/config_manager.py: 配置管理，状态参数控制
   - src/utils/logger.py: 日志系统，状态变更记录
   - src/utils/cuda_manager.py: 系统资源监控与GPU管理
   - src/utils/path_utils.py: 路径工具，检查点存储管理
   - src/utils/resource_manager.py: 资源管理，资源状态追踪
   - src/utils/exception_handler.py: 异常处理，错误状态管理

2. 共用模块:
   - src/models/gan/trainer.py: 训练器，训练状态更新
   - src/models/gan/gan_evaluator.py: 评估器，评估状态跟踪
   - src/data/metrics_calculator.py: 指标计算，性能指标记录

3. 配置文件:
   - config.yaml:
     ├── training:
     │   ├── early_stopping:
     │   │   ├── patience: 早停耐心值
     │   │   ├── min_delta: 最小改善阈值
     │   │   └── monitor: 监控指标
     │   ├── checkpointing:
     │   │   ├── frequency: 保存频率
     │   │   ├── keep_best: 保留最佳模型
     │   │   └── max_to_keep: 最大保留数量
     │   └── metrics:
     │       ├── primary: 主要评估指标
     │       └── secondary: 次要评估指标
     └── system:
         ├── recovery: 恢复机制配置
         └── logging: 状态日志配置

4. 父类模块:
   - src/models/base/base_model.py: BaseModel，模型基类
   - src/models/base/base_module.py: BaseModule，基础功能模块
   - src/utils/base/base_state.py: BaseState，状态基类
   - src/utils/base/base_manager.py: BaseManager，管理器基类

5. 同阶段基础模型模块:
   - base_model.py: 模型核心功能
   - base_module.py: 基础模块功能
   - signal_processor.py: 信号处理功能
   - model_saver.py: 模型存储功能

核心功能：
1. 状态生命周期管理
   - 状态定义与初始化
   - 状态转换与验证
   - 状态序列化与恢复
   - 训练状态报告

2. 训练状态追踪
   - 轮次与批次记录
   - 性能指标追踪
   - 梯度统计收集
   - 学习率变化记录

3. 早停机制实现
   - 耐心值管理
   - 性能监控
   - 停止条件评估
   - 最佳模型判断

4. 检查点管理
   - 保存时机判断
   - 检查点创建
   - 状态恢复逻辑
   - 清理策略实现

5. 错误处理与恢复
   - 错误状态标记
   - 组件状态监控
   - 恢复操作协调
   - 异常信息记录
"""

import threading
import time
from collections import defaultdict
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum, auto
from pathlib import Path
from typing import Any

import torch

from src.utils.config.validator import ConfigValidator, ValidationRule


class ModelState(Enum):
    """模型状态枚举及允许的状态转换"""
    INITIALIZED = auto()      # 初始化完成
    TRAINING = auto()         # 训练中
    EVALUATING = auto()       # 评估中
    ERROR = auto()            # 错误状态

    @classmethod
    def get_valid_transitions(cls) -> dict[Any, set]:
        """获取有效的状态转换规则"""
        return {
            cls.INITIALIZED: {cls.TRAINING, cls.EVALUATING, cls.ERROR},
            cls.TRAINING: {cls.EVALUATING, cls.ERROR, cls.TRAINING},  # 允许自循环
            cls.EVALUATING: {cls.TRAINING, cls.ERROR, cls.INITIALIZED},
            cls.ERROR: {cls.INITIALIZED}  # 只能从ERROR状态恢复到INITIALIZED
        }

@dataclass
class TrainingState:
    """训练状态数据类"""
    epoch: int = 0                           # 当前轮次
    best_loss: float = float('inf')          # 最佳损失
    patience_counter: int = 0                # 早停计数器
    train_metrics: dict = field(default_factory=dict)  # 训练指标
    val_metrics: dict = field(default_factory=dict)    # 验证指标
    last_checkpoint: Path | None = None   # 最后检查点路径
    last_save_time: datetime | None = None

    # 训练过程元数据
    gradient_norms: dict[str, float] = field(default_factory=dict)  # 各层梯度范数
    learning_rates: list[float] = field(default_factory=list)       # 学习率变化序列
    timing_metrics: dict[str, float] = field(default_factory=dict)  # 训练耗时统计


class ModelStateManager(ConfigValidator):
    """模型状态管理器

    新增方法：
    - set_current_epoch: 快速更新当前训练轮次
    """

    _instance = None
    _initialized: bool = False  # 正确声明类属性

    def __new__(cls, config=None):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
            cls._instance.config = config
            # 在__new__方法中初始化logger，确保单例模式下logger总是存在
            from src.utils.logger import get_logger
            cls._instance.logger = get_logger("ModelStateManager")
        return cls._instance



    def get_component_states(self) -> dict[str, bool]:
        """获取所有组件状态"""
        return self._component_states.copy()

    def __init__(self, config: Any):
        """初始化模型状态管理器

        Args:
            config: 配置对象 (必须包含training.early_stopping配置)

        Raises:
            ValueError: 如果config无效或缺少必要配置
        """
        # 防止重复初始化
        if self._initialized:
            return

        if config is None:
            raise ValueError("config参数不能为None")

        # 调用父类初始化，确保logger已经初始化
        super().__init__(logger=self.logger)

        # 添加验证规则
        self.add_rule(ValidationRule(
            field_path="training.early_stopping.patience",
            field_type=int,
            required=True,
            min_value=1
        ))

        # 验证配置
        self.validate_or_raise(config)

        # 初始化锁（logger已在__new__中初始化）
        self._lock = threading.RLock()  # 使用可重入锁

        with self._lock:
            # 初始化基本状态
            self.current_state = ModelState.INITIALIZED
            self.training_state = TrainingState()
            self.error_message: str | None = None
            self._component_states = {}
            self._state_history = defaultdict(list)
            self._early_stopping_patience = self._get_early_stopping_patience(config)
            self.config = config

            # 初始化模型和优化器引用
            self._model = None
            self._optimizer = None

            # 初始化梯度和学习率跟踪
            self.training_state.gradient_norms = {}
            self.training_state.learning_rates = []

            self._initialized = True

        self.logger.info(
            f"状态管理器初始化完成:\n"
            f"- 当前状态: {self.current_state.name}\n"
            f"- 训练轮次: {self.training_state.epoch}\n"
            f"- 最佳损失: {self.training_state.best_loss}"
        )

    def set_model_optimizer(self, model: torch.nn.Module | None = None,
                        optimizer: torch.optim.Optimizer | None = None):
        """设置模型和优化器引用,用于梯度和学习率跟踪

        Args:
            model: 模型实例
            optimizer: 优化器实例
        """
        with self._lock:
            if model is not None:
                self._model = model
            if optimizer is not None:
                self._optimizer = optimizer

    def _collect_gradient_stats(self) -> dict[str, float]:
        """收集梯度统计信息

        Returns:
            Dict[str, float]: 梯度统计信息字典
        """
        stats = {}
        try:
            if self._model is not None:
                for name, param in self._model.named_parameters():
                    if param.grad is not None:
                        grad_norm = torch.norm(param.grad).item()
                        stats[f"grad_norm_{name}"] = grad_norm
        except Exception as e:
            self.logger.warning(f"收集梯度统计失败: {e!s}")
        return stats

    def _get_current_lr(self) -> float:
        """获取当前学习率

        Returns:
            float: 当前学习率,如果获取失败则返回0.0
        """
        try:
            if self._optimizer is not None:
                return self._optimizer.param_groups[0]['lr']
        except Exception as e:
            self.logger.warning(f"获取学习率失败: {e!s}")
        return 0.0


    def _get_early_stopping_patience(self, config: Any) -> int:
        """获取早停耐心值"""
        return config.training.early_stopping.patience

    def transition_to(self, new_state: ModelState):
        """线程安全的状态转换

        Args:
            new_state: 目标状态

        Raises:
            ValueError: 如果状态转换无效
        """
        with self._lock:
            # 获取有效转换规则
            valid_transitions = ModelState.get_valid_transitions()

            # 验证转换合法性
            if new_state not in valid_transitions[self.current_state]:
                error_msg = (
                    f"无效的状态转换:\n"
                    f"- 当前状态: {self.current_state.name}\n"
                    f"- 目标状态: {new_state.name}\n"
                    f"- 允许转换: {[s.name for s in valid_transitions[self.current_state]]}"
                )
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # 执行状态转换
            old_state = self.current_state
            self.current_state = new_state
            self._state_history[new_state].append(time.time())

            self.logger.info(
                f"状态转换成功:\n"
                f"- 原状态: {old_state.name}\n"
                f"- 新状态: {new_state.name}"
            )

            if new_state == ModelState.ERROR:
                self.logger.error(f"进入错误状态: {self.error_message}")

    def update_training_state(
        self,
        epoch: int,
        train_metrics: dict[str, float],
        val_metrics: dict[str, float]
    ):
        """线程安全的训练状态更新

        Args:
            epoch: 当前轮次
            train_metrics: 训练指标
            val_metrics: 验证指标

        Raises:
            ValueError: 如果指标数据无效
        """
        if not isinstance(train_metrics, dict) or not isinstance(val_metrics, dict):
            raise ValueError("训练和验证指标必须是字典类型")

        if 'val_loss' not in val_metrics:
            raise ValueError("验证指标中必须包含'val_loss'")

        with self._lock:
            # 验证当前状态
            if self.current_state not in [ModelState.TRAINING, ModelState.EVALUATING]:
                raise ValueError(f"无效的状态下更新训练状态: {self.current_state.name}")

            # 更新状态
            self.training_state.epoch = epoch
            self.training_state.train_metrics = train_metrics.copy()
            self.training_state.val_metrics = val_metrics.copy()

            # 更新最佳损失
            current_loss = val_metrics['val_loss']
            if current_loss < self.training_state.best_loss:
                self.training_state.best_loss = current_loss
                self.training_state.patience_counter = 0
                self.logger.info(
                    f"发现新的最佳损失:\n"
                    f"- 轮次: {epoch}\n"
                    f"- 损失: {current_loss:.4f}"
                )
            else:
                self.training_state.patience_counter += 1
                self.logger.info(
                    f"损失未改善:\n"
                    f"- 轮次: {epoch}\n"
                    f"- 当前损失: {current_loss:.4f}\n"
                    f"- 最佳损失: {self.training_state.best_loss:.4f}\n"
                    f"- 耐心计数: {self.training_state.patience_counter}"
                )

            # 记录梯度和学习率信息
            if hasattr(self.training_state, 'gradient_norms'):
                self.training_state.gradient_norms = self._collect_gradient_stats()
            if hasattr(self.training_state, 'learning_rates'):
                self.training_state.learning_rates.append(self._get_current_lr())

    def should_save_checkpoint(self, epoch: int) -> bool:
        """检查是否应该保存检查点

        Args:
            epoch: 当前轮次

        Returns:
            bool: 是否应该保存检查点

        Raises:
            ValueError: 如果无法读取检查点保存频率
        """
        # 从配置中读取保存频率，不使用默认值
        save_frequency = None

        try:
            if hasattr(self.config, 'training') and hasattr(self.config.training, 'checkpoint'):
                checkpoint_config = self.config.training.checkpoint
                if hasattr(checkpoint_config, 'save_freq'):
                    save_frequency = checkpoint_config.save_freq
                elif isinstance(checkpoint_config, dict) and 'save_freq' in checkpoint_config:
                    save_frequency = checkpoint_config['save_freq']

                if save_frequency is None:
                    error_msg = "配置中缺少checkpoint.save_freq字段"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)

                self.logger.debug(f"检查点保存频率: {save_frequency}")
            else:
                error_msg = "配置中缺少training.checkpoint配置"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
        except Exception as e:
            if isinstance(e, ValueError):
                raise
            error_msg = f"读取检查点保存频率失败: {e!s}"
            self.logger.error(error_msg)
            raise ValueError(error_msg) from e

        # 每save_frequency轮保存一次，或者是最后一轮，或者是第一轮
        return epoch % save_frequency == 0 or epoch in (self.training_state.epoch, 1)

    def set_early_stopping_patience(self, patience: int):
        """设置早停耐心值

        Args:
            patience: 早停轮数
        """
        if patience > 0:
            self._early_stopping_patience = patience
            self.logger.info(f"更新早停耐心值: {patience}")

    def should_stop_training(self) -> bool:
        """检查是否应该停止训练

        Returns:
            bool: 是否应该停止训练
        """
        return self.training_state.patience_counter >= self._early_stopping_patience

    def set_error(self, error_message: str):
        """设置错误状态

        Args:
            error_message: 错误信息
        """
        self.error_message = error_message
        self.transition_to(ModelState.ERROR)

    def get_state_info(self) -> dict[str, Any]:
        """获取状态信息

        Returns:
            Dict: 状态信息
        """
        try:
            info = {
                'state': self.current_state.name,
                'epoch': self.training_state.epoch,
                'best_loss': float(self.training_state.best_loss),
                'patience_counter': self.training_state.patience_counter
            }

            self.logger.debug(
                f"获取状态信息:\n"
                f"- 当前状态: {info['state']}\n"
                f"- 当前轮次: {info['epoch']}\n"
                f"- 最佳损失: {info['best_loss']:.4f}\n"
                f"- 耐心计数: {info['patience_counter']}"
            )

            return info

        except Exception as e:
            self.logger.error(
                f"获取状态信息失败:\n"
                f"- 错误类型: {type(e).__name__}\n"
                f"- 错误信息: {e!s}",
                exc_info=True
            )
            raise

    def reset(self):
        """重置状态"""
        self.current_state = ModelState.INITIALIZED
        self.training_state = TrainingState()
        self.error_message = None
        self.logger.info("状态已重置")

    def validate_state(self, expected_state: ModelState) -> bool:
        """验证当前状态

        Args:
            expected_state: 期望的状态

        Returns:
            bool: 是否匹配
        """
        try:
            is_valid = self.current_state == expected_state

            if not is_valid:
                self.logger.warning(
                    f"状态不匹配:\n"
                    f"- 当前状态: {self.current_state}\n"
                    f"- 期望状态: {expected_state}\n"
                    f"- 当前轮次: {self.training_state.epoch}\n"
                    f"- 最佳损失: {self.training_state.best_loss:.4f}"
                )
            else:
                self.logger.debug(
                    f"状态验证通过:\n"
                    f"- 当前状态: {self.current_state}\n"
                    f"- 期望状态: {expected_state}"
                )

            return is_valid

        except Exception as e:
            self.logger.error(
                f"验证状态失败:\n"
                f"- 错误类型: {type(e).__name__}\n"
                f"- 错误信息: {e!s}\n"
                f"- 当前状态: {self.current_state}\n"
                f"- 期望状态: {expected_state}",
                exc_info=True
            )
            return False

    def _prepare_serializable_history(self, history: dict[str, list]) -> list[dict[str, float]]:
        """准备可序列化的历史记录

        Args:
            history: 原始历史记录

        Returns:
            List: 可序列化的历史记录
        """
        try:
            serializable = []
            for epoch_data in zip(*history.values(), strict=False):
                entry = {}
                for key, value in zip(history.keys(), epoch_data, strict=False):
                    entry[key] = float(value) if isinstance(value, torch.Tensor) else value
                serializable.append(entry)

            self.logger.debug(
                f"历史记录序列化:\n"
                f"- 指标数量: {len(history)}\n"
                f"- 轮次数量: {len(serializable)}\n"
                f"- 指标类型: {list(history.keys())}"
            )

            return serializable

        except Exception as e:
            self.logger.error(
                f"序列化历史记录失败:\n"
                f"- 错误类型: {type(e).__name__}\n"
                f"- 错误信息: {e!s}\n"
                f"- 历史记录键: {list(history.keys())}",
                exc_info=True
            )
            raise

    def is_best_model(self, metrics: dict[str, float] | None) -> bool:
        """判断当前模型是否是最佳模型

        Args:
            metrics: 评估指标字典

        Returns:
            bool: 是否是最佳模型
        """
        try:
            if not metrics or 'val_loss' not in metrics:
                self.logger.warning(
                    f"无法判断最佳模型:\n"
                    f"- 指标为空: {metrics is None}\n"
                    f"- 验证损失缺失: {'val_loss' not in metrics if metrics else True}"
                )
                return False

            current_loss = metrics['val_loss']
            is_best = current_loss < self.training_state.best_loss

            self.logger.info(
                f"最佳模型判断:\n"
                f"- 当前损失: {current_loss:.4f}\n"
                f"- 最佳损失: {self.training_state.best_loss:.4f}\n"
                f"- 是否最佳: {'是' if is_best else '否'}"
            )

            return is_best

        except Exception as e:
            self.logger.error(
                f"判断最佳模型失败:\n"
                f"- 错误类型: {type(e).__name__}\n"
                f"- 错误信息: {e!s}\n"
                f"- 当前指标: {metrics}",
                exc_info=True
            )
            return False

    def log_component_state(self, component_name: str, state: bool, details: str = ""):
        """记录组件状态变化

        Args:
            component_name: 组件名称
            state: 组件状态
            details: 详细信息
        """
        # 检查状态是否真的发生变化
        old_state = self._component_states.get(component_name)
        if old_state != state:
            self._component_states[component_name] = state
            self.logger.info(
                f"组件状态更新 [{component_name}]:\n"
                f"- 状态: {'已初始化' if state else '未初始化'}\n"
                f"- 详情: {details if details else 'N/A'}\n"
                f"- 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )

    def check_component_state(self, component_name: str) -> bool:
        """检查组件状态

        Args:
            component_name: 组件名称

        Returns:
            bool: 组件是否已初始化

        Raises:
            RuntimeError: 当组件未初始化时抛出
        """
        is_initialized = self._component_states.get(component_name, False)
        if not is_initialized:
            error_msg = f"组件状态检查失败 [{component_name}]:\n- 当前状态: 未初始化\n- 所有状态: {self._component_states}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)
        return is_initialized

    def handle_gradient_none(self, tensor: torch.Tensor, name: str = "", epsilon: float = 1e-6) -> torch.Tensor:
        """统一处理梯度为None的情况

        Args:
            tensor: 输入张量
            name: 张量名称，用于日志
            epsilon: 数值稳定性参数

        Returns:
            torch.Tensor: 处理后的张量
        """
        try:
            # 1. 检查张量是否有效
            if tensor is None:
                raise ValueError(f"{name}张量为None")

            # 2. 数值稳定性处理 (放宽范围)
            tensor = torch.clamp(tensor, -1e5, 1e5) # Range widened from 1e3 to 1e5

            # 3. 梯度连接检查
            if not tensor.requires_grad:
                error_msg = f"{name}没有梯度连接，这是一个关键错误"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # 4. 检查数值是否有效
            if torch.isnan(tensor).any() or torch.isinf(tensor).any():
                error_msg = f"{name}包含NaN/Inf值，这是一个关键错误"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            return tensor

        except Exception as e:
            self.logger.error(
                f"处理梯度为None失败:\n"
                f"- 错误类型: {type(e).__name__}\n"
                f"- 错误信息: {e!s}\n"
                f"- 张量名称: {name}",
                exc_info=True
            )
            raise
