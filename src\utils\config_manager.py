"""配置管理系统 - 提供统一的配置控制与验证框架

此模块已重构为按功能领域组织的模块结构，位于src.utils.config包中。
为保持向后兼容性，此模块重新导出所有配置类和函数。
"""

# 重新导出所有配置类和函数
from src.utils.config import (
    BalanceConfig,
    # 基础配置
    BaseConfig,
    # 模型配置
    BaseModelConfig,
    BatchSizeOptimizerConfig,
    CheckpointConfig,
    # 配置管理器
    ConfigManager,
    # 数据配置
    DataConfig,
    DiscriminatorConfig,
    EarlyStoppingConfig,
    EvaluationConfig,
    FeatureEngineeringConfig,
    FeaturesConfig,
    FeatureValidationConfig,
    GANModelConfig,
    # 移除DimensionAdapterConfig，因为维度适配功能已经集成到生成器和判别器中
    GeneratorConfig,
    LoggingConfig,
    LossConfig,
    LrBalancerConfig,
    MixedPrecisionConfig,
    OptimizerConfig,
    # 路径配置
    PathsConfig,
    # 预测配置 (从新模块导入)
    PredictionConfig,
    SchedulerConfig,
    SequenceStrategyConfig,
    # 系统配置
    SystemConfig,
    # 训练配置
    TrainingConfig,
    get_config,
)

# 导出所有公共接口
__all__ = [
    'BalanceConfig',
    # 基础配置
    'BaseConfig',
    # 模型配置
    'BaseModelConfig',
    'BatchSizeOptimizerConfig',
    'CheckpointConfig',
    # 配置管理器
    'ConfigManager',
    # 数据配置
    'DataConfig',
    'DiscriminatorConfig',
    'EarlyStoppingConfig',
    'EvaluationConfig',
    'FeatureEngineeringConfig',
    'FeatureValidationConfig',
    'FeaturesConfig',
    'GANModelConfig',
    # 移除DimensionAdapterConfig，因为维度适配功能已经集成到生成器和判别器中
    'GeneratorConfig',
    'LoggingConfig',
    'LossConfig',
    'LrBalancerConfig',
    'MixedPrecisionConfig',
    'OptimizerConfig',
    # 路径配置
    'PathsConfig',
    # 预测配置
    'PredictionConfig',
    'SchedulerConfig',
    'SequenceStrategyConfig',
    # 系统配置
    'SystemConfig',
    # 训练配置
    'TrainingConfig',
    'get_config'
]

import json
import logging
from typing import Any, Dict
from pathlib import Path

# from src.config.config import load_config # 移除旧的导入

logger = logging.getLogger(__name__)


class PathEncoder(json.JSONEncoder):
    """处理Path对象的JSON编码器"""
    def default(self, obj):
        if isinstance(obj, Path):
            return str(obj)
        return super().default(obj)


