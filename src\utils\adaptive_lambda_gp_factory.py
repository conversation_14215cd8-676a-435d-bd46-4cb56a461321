"""
自适应梯度惩罚权重配置工厂

提供从ConfigManager创建AdaptiveLambdaGPConfig的工厂函数。
"""
import logging
from logging import Logger
from typing import Any

from src.utils.adaptive_lambda_gp_config import AdaptiveLambdaGPConfig


def create_adaptive_lambda_gp_config(
    config_manager: Any,
    logger: Logger | None = None
) -> AdaptiveLambdaGPConfig:
    """
    从ConfigManager创建AdaptiveLambdaGPConfig

    Args:
        config_manager: 配置管理器实例
        logger: 日志记录器，如果为None则创建新的记录器

    Returns:
        AdaptiveLambdaGPConfig: 创建的配置对象
    """
    logger = logger or logging.getLogger("AdaptiveLambdaGPFactory")

    # 默认配置 - 使用更合理的默认值
    config_dict = {
        "enabled": False,
        "base_lambda_gp": None,
        "min_lambda_gp": 2.0,   # 从14.0降低到2.0，允许更大的调整空间
        "max_lambda_gp": 15.0,  # 从20.0降低到15.0，避免过高的梯度惩罚
        "adaptation_rate": 0.05,
        "warmup_steps": 100,
        "update_interval": 10,
        "smoothing_factor": 0.7,
        "grad_norm_target": 1.0,
        "grad_norm_tolerance": 0.5,
        "verbose_logging": True
        # 已删除 "save_history_plot" 和 "history_plot_path"，因为这是之前彻底删除的设计
    }

    # 尝试从配置管理器获取自适应梯度惩罚权重配置
    try:
        # 检查是否有training.adaptive_lambda_gp配置
        if hasattr(config_manager, 'training') and hasattr(config_manager.training, 'adaptive_lambda_gp'):
            adaptive_config = config_manager.training.adaptive_lambda_gp

            # 如果是字典，直接更新
            if isinstance(adaptive_config, dict):
                config_dict.update(adaptive_config)
                logger.info("从配置字典加载自适应梯度惩罚权重配置")
            # 如果是对象，转换为字典
            elif hasattr(adaptive_config, '__dict__'):
                obj_dict = {k: v for k, v in adaptive_config.__dict__.items()
                           if not k.startswith('_')}
                config_dict.update(obj_dict)
                logger.info("从配置对象加载自适应梯度惩罚权重配置")
            else:
                logger.warning(f"无法识别的adaptive_lambda_gp配置类型: {type(adaptive_config)}")

        # 检查是否有单独的配置项
        if hasattr(config_manager, 'training'):
            # 检查是否启用
            if hasattr(config_manager.training, 'use_adaptive_lambda_gp'):
                config_dict['enabled'] = config_manager.training.use_adaptive_lambda_gp

            # 检查基准lambda_gp值
            if hasattr(config_manager.training, 'lambda_gp'):
                config_dict['base_lambda_gp'] = config_manager.training.lambda_gp

            # 检查其他可能的配置项
            for key in ['lambda_gp_min', 'lambda_gp_max', 'lambda_gp_adaptation_rate',
                        'lambda_gp_warmup_steps', 'lambda_gp_update_interval']:
                if hasattr(config_manager.training, key):
                    # 转换键名格式
                    config_key = key.replace('lambda_gp_', '')
                    config_dict[config_key] = getattr(config_manager.training, key)

        # 已删除设置历史图表保存路径的代码，因为这是之前彻底删除的设计

        # 检查是否有模型配置
        use_self_attention = False
        if hasattr(config_manager, 'model') and hasattr(config_manager.model, 'use_self_attention'):
            use_self_attention = config_manager.model.use_self_attention

        # 注释掉自注意力机制对min_lambda_gp的强制调整，允许用户自定义配置
        # if use_self_attention and config_dict['min_lambda_gp'] < 16.0:
        #     logger.info(f"检测到使用自注意力机制，将min_lambda_gp从{config_dict['min_lambda_gp']}调整为16.0")
        #     config_dict['min_lambda_gp'] = 16.0
        logger.info(f"保持用户配置的min_lambda_gp值: {config_dict['min_lambda_gp']}")

    except Exception as e:
        logger.error(f"从配置管理器创建自适应梯度惩罚权重配置时出错: {e!s}")

    # 创建配置对象
    config = AdaptiveLambdaGPConfig.from_dict(config_dict)

    # 记录配置信息
    logger.info(f"创建的自适应梯度惩罚权重配置: {config}")

    return config
