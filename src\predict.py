"""预测模块 - 提供时序GAN模型的推理与结果生成框架

项目结构模块索引：
1. 基础设施模块:
   - src/utils/config_manager.py: 配置管理，预测参数控制
   - src/utils/logger.py: 日志系统，预测过程记录
   - src/utils/cuda_manager.py: 系统监控，预测性能追踪
   - src/utils/path_utils.py: 路径工具，输出结果管理
   - src/utils/resource_manager.py: 资源管理，预测资源分配
   - src/utils/exception_handler.py: 异常处理，预测错误恢复
   - src/utils/cuda_manager.py: GPU管理，预测加速支持

2. 共用模块:
   - src/models/gan/gan_model.py: GAN模型，预测核心模型
   - src/models/gan/gan_evaluator.py: 评估器，预测结果评估
   - src/data/window_dataset.py: 时序数据集，预测数据管理
   - src/data/metrics_calculator.py: 指标计算，结果质量评估
   - src/data/data_loader.py: 数据加载，生产环境数据处理

3. 配置文件:
   - config.yaml:
     ├── prediction:
     │   ├── batch_size: 批量预测大小
     │   ├── confidence: 置信度计算参数
     │   ├── cache: 缓存策略配置
     │   ├── validation: 预测验证策略
     │   └── output: 输出格式配置
     ├── model:
     │   ├── generator: 生成器配置
     │   └── feature_extractor: 特征提取配置
     └── system:
         ├── device: 设备配置参数
         ├── precision: 计算精度设置
         └── memory: 内存优化参数

4. 预测流程组件:
   - 预测准备: 模型加载与参数设置
   - 批量处理: 大规模数据推理
   - 单点预测: 实时数据处理
   - 置信度计算: 结果可靠性评估
   - 性能优化: 缓存与批处理策略

核心功能：
1. 预测接口实现
   - 序列批量预测
   - 单一样本预测
   - 条件性预测生成
   - 增量序列更新
   - 实时流数据处理

2. 输出处理与转换
   - 结果格式化与保存
   - 置信区间计算
   - 数据后处理与规范化
   - 预测可视化支持
   - 输出兼容性适配

3. 资源管理与优化
   - 批处理大小自适应
   - 预测缓存管理
   - 批量并行预测
   - 内存使用监控
   - CPU/GPU资源平衡

4. 健壮性与可靠性
   - 异常预测检测
   - 数值稳定性保障
   - 错误恢复与重试
   - 超时控制与中断
   - 边界条件处理

5. 性能监控与分析
   - 预测时延统计
   - 吞吐量监控
   - 资源使用跟踪
   - 预测质量评估
   - 性能瓶颈识别
"""

from collections.abc import Mapping
from typing import (
    TypedDict,
)

import numpy as np
import torch
from torch import Tensor

from src.data.standardization import StandardizationProtocol


# 核心类型定义
class DataItem(TypedDict):
    """数据项类型 - 包含特征和目标
    Attributes:
        features: 输入特征张量 [batch_size, seq_len, feature_dim]
        target: 目标值张量 [batch_size, target_dim]
    """
    features: Tensor
    target: Tensor

class DataDict(TypedDict):
    features: Tensor
    target: Tensor

PredictResult = Mapping[str, Tensor | dict[str, float]]

import time

from torch.cuda import amp

from src.data.data_loader import TimeSeriesDataLoaderProtocol
from src.data.windowed_time_series import TimeSeriesWindowDataset
from src.models.gan.gan_evaluator import GANEvaluator
from src.models.gan.gan_model import GANModel

# from src.models.gan.comprehensive_loss import ComprehensiveLoss # 已删除，不再使用
from src.utils.config_manager import ConfigManager
from src.utils.cuda import cuda_manager
from src.utils.cuda.manager import CUDAManager
from src.utils.logger import get_logger
from src.utils.resource_manager import ResourceManager


class Predictor:
    """预测接口层 - 提供简单预测API

    架构定位: 接口层预测接口
    依赖模块:
    - src/models/gan/gan_model.py (核心模型)
    - src/utils/config_manager.py (配置)
    - src/utils/cuda_manager.py (设备管理)

    设计原则:
    1. 保持接口简单
    2. 内部调用PredictionRunner
    3. 处理输入输出转换
    """

    def __init__(self, config: ConfigManager, cuda_manager: CUDAManager):
        """初始化预测接口

        Args:
            config: 配置管理器实例
            cuda_manager: CUDA管理器实例
        """
        self.logger = get_logger(self.__class__.__name__)
        self.config = config
        self.device = cuda_manager.device

        # 初始化核心预测组件
        # 从数据预处理结果中获取特征维度
        if not hasattr(config.data, 'feature_dim'):
            raise ValueError("config.data中必须设置feature_dim属性")
        self.model = GANModel(config, feature_dim=config.data.feature_dim)
        self.resource_manager = ResourceManager(config)

        # 从数据加载器中获取标准化器(如果可用)
        if not hasattr(config, 'data_loader'):
            raise ValueError("config必须包含data_loader属性")
        if not hasattr(config.data_loader, 'get_target_standardizer'):
            raise ValueError("config.data_loader必须包含get_target_standardizer方法")

        target_standardizer = config.data_loader.get_target_standardizer()

        if not hasattr(config.data_loader, 'target_scale_factor'):
            self.logger.warning("config.data_loader中缺少target_scale_factor属性，使用默认值1.0")
            target_scale_factor = 1.0
        else:
            target_scale_factor = config.data_loader.target_scale_factor

        self.runner = PredictionRunner(
            model=self.model,
            config=config,
            resource_manager=self.resource_manager,
            target_standardizer=target_standardizer,
            target_scale_factor=target_scale_factor
        )

    def predict(self, features: np.ndarray) -> np.ndarray:
        """执行预测(接口层方法)

        Args:
            features: 输入特征数组 [batch_size, feature_dim]
                     支持动态特征维度

        Returns:
            np.ndarray: 预测结果数组 [batch_size]
        """
        # 输入验证
        if len(features.shape) != 2 or features.shape[0] == 0 or features.shape[1] == 0:
            raise ValueError(f"特征形状无效: {features.shape}")

        # 记录当前特征维度
        feature_dim = features.shape[1]
        self.logger.info(f"当前输入特征维度: {feature_dim}")

        # 转换为tensor并添加序列维度
        features_tensor = torch.from_numpy(features).float()
        features_tensor = features_tensor.unsqueeze(1)  # [batch_size, 1, feature_dim]

        # 调用核心预测逻辑
        result = self.runner.predict_sequence(
            features_tensor,
            return_confidence=False,
            batch_size=None,
            use_cache=True
        )

        # 转换输出格式
        predictions = result.get('predictions')
        if not isinstance(predictions, Tensor):
            # 不再使用回退逻辑，直接抛出异常
            error_msg = f"预测结果 'predictions' 类型不正确: {type(predictions)}"
            self.logger.error(error_msg)
            raise TypeError(error_msg)

        return predictions.squeeze().cpu().numpy()

class PredictionRunner:
    """预测运行器

    专注于value15的未来预测：
    1. 递归式预测
    2. 预测区间估计
    3. 不同预测模式支持
    """

    def __init__(
        self,
        model: GANModel,
        config: ConfigManager,
        resource_manager: ResourceManager,
        target_standardizer: StandardizationProtocol,
        target_scale_factor: float
    ):
        """初始化预测运行器

        Args:
            model: 训练好的GAN模型
            config: 配置管理器实例
            resource_manager: 资源管理器实例
            target_standardizer: 目标标准化器实例
            target_scale_factor: 目标缩放因子
        """
        self.logger = get_logger(self.__class__.__name__)

        self.model = model
        self.config = config
        self.resource_manager = resource_manager
        self.device = cuda_manager.device  # 使用cuda_manager获取设备

        # 从配置中获取预测步数
        if not hasattr(config.data, 'prediction_horizon'):
            error_msg = "config.data中缺少prediction_horizon属性"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        self.prediction_horizon = config.data.prediction_horizon

        # 从配置中获取采样次数
        # PredictionConfig now has n_samples with a default value, so direct access is safe.
        # The __post_init__ in PredictionConfig will validate it.
        self.n_samples = config.prediction.n_samples

        # 初始化评估器
        self.evaluator = GANEvaluator.from_config(config)

        # 初始化目标标准化器
        self.target_standardizer = target_standardizer
        self.target_scale_factor = target_scale_factor

        self.logger.info(
            f"预测运行器初始化完成:\n"
            f"- 预测步数: {self.prediction_horizon}\n"
            f"- 采样次数: {self.n_samples}\n"
            f"- 计算设备: {self.device}\n"
            f"- 模型类型: {type(model).__name__}\n"
            f"- 评估器: {type(self.evaluator).__name__}\n"
            f"- 目标标准化器: {'已设置' if target_standardizer else '未设置'}"
        )

    def _inverse_transform_predictions(self, predictions: dict[str, Tensor]) -> dict[str, Tensor]:
        """对预测结果应用逆标准化变换

        Args:
            predictions: 包含预测结果的字典

        Returns:
            逆变换后的预测结果
        """
        if self.target_standardizer is None:
            self.logger.warning("目标标准化器未设置，跳过逆标准化")
            return predictions

        try:
            result = {}
            for key, tensor in predictions.items():
                if key in ['predictions', 'lower_bound', 'upper_bound']:
                    # 应用逆标准化并还原缩放因子
                    result[key] = self.target_standardizer.inverse_transform(tensor) / self.target_scale_factor
                    self.logger.debug(f"已对{key}应用逆标准化")
                else:
                    result[key] = tensor
            return result
        except Exception as e:
            self.logger.error(f"逆标准化失败: {e!s}")
            return predictions

    def predict_sequence(
        self,
        features: torch.Tensor,
        return_confidence: bool,
        batch_size: int | None,
        use_cache: bool
    ) -> PredictResult:
        """直接预测未来序列

        Args:
            features: 输入特征 [batch_size, sequence_length, feature_dim]
            return_confidence: 是否返回置信区间

        Returns:
            Dict[str, torch.Tensor]: 预测结果，包含：
                - predictions: [batch_size, prediction_horizon] 预测序列
                - lower_bound: [batch_size, prediction_horizon] 下界（如果return_confidence=True）
                - upper_bound: [batch_size, prediction_horizon] 上界（如果return_confidence=True）
        """
        try:
            start_time = time.time()
            self.model.eval()

            # 缓存检查由resource_manager统一处理
            cached_result = self.resource_manager.get_prediction_if_cached(features, return_confidence, use_cache)
            if cached_result is not None:
                predict_time = time.time() - start_time
                self.logger.info(f"从缓存获取预测结果，用时: {predict_time:.4f}秒")
                return cached_result

            # 核心预测逻辑
            with torch.no_grad(), amp.autocast(enabled=True):
                # 确保特征维度与模型匹配
                if features.size(2) != self.model.current_feature_dim:
                    self.logger.warning(f"特征维度不匹配: 输入{features.size(2)}维, 模型{self.model.current_feature_dim}维")
                    self.model._update_discriminator_input_dim(features.size(2))

                # 生成噪声 - 确保维度与特征匹配
                noise = torch.randn(
                    features.size(0) * self.n_samples,
                    features.size(1),  # 序列长度
                    self.model.noise_dim,
                    device=self.device,
                    names=None # 显式添加 names=None 以修复 Pylance 错误
                )

                # 复制特征并预测
                repeated_features = features.repeat(self.n_samples, 1, 1).to(self.device)
                # 使用generate方法而非直接调用forward
                generated_output = self.model.generate(repeated_features, noise)

                # 处理预测结果 - 确保是 Tensor 类型
                if not isinstance(generated_output, Tensor):
                    error_msg = f"generate 返回了意外的类型: {type(generated_output)}"
                    self.logger.error(error_msg)
                    raise TypeError(error_msg)

                predictions_tensor = generated_output

                # 现在可以安全地调用 view
                predictions_tensor = predictions_tensor.view(self.n_samples, features.size(0), -1, 1)
                mean_predictions = predictions_tensor.mean(dim=0)
                result = {'predictions': mean_predictions}

                if return_confidence:
                    # 使用 predictions_tensor 计算分位数
                    lower_bound = torch.quantile(predictions_tensor, 0.025, dim=0)
                    upper_bound = torch.quantile(predictions_tensor, 0.975, dim=0)
                    result.update({
                        'lower_bound': lower_bound,
                        'upper_bound': upper_bound
                    })

                # 验证结果
                self.evaluator.validate_predictions(mean_predictions)
                predict_time = time.time() - start_time

                # 应用逆标准化(如果标准化器可用)
                if self.target_standardizer is not None:
                    result = self._inverse_transform_predictions(result)
                    self.logger.info(
                        f"已对预测结果应用逆标准化\n"
                        f"- 缩放因子: {self.target_scale_factor}"
                    )

                self.logger.info(
                    f"序列预测完成:\n"
                    f"- 预测时间: {predict_time:.2f}秒\n"
                    f"- 样本数量: {features.size(0)}\n"
                    f"- 预测形状: {mean_predictions.shape}\n"
                    f"- 是否应用逆标准化: {'是' if self.target_standardizer else '否'}"
                )
                return result

        except Exception as e:
            self.logger.error(f"预测失败: {e!s}")
            raise

    def _log_standardization_status(self, dataset):
        """记录标准化处理状态"""
        if hasattr(dataset, 'standardization'):
            self.logger.info(
                f"标准化状态: {dataset.standardization.__class__.__name__}\n"
                f"均值: {dataset.standardization.mean}\n"
                f"标准差: {dataset.standardization.std}"
            )

    def predict_batch(
        self,
        dataloader: TimeSeriesDataLoaderProtocol
    ) -> PredictResult:
        """批量预测

        Args:
            dataloader: 符合时序数据加载器协议的数据加载器，
                     必须实现TimeSeriesDataLoaderProtocol接口

        Returns:
            PredictResult: 预测结果，包含：
                - predictions: [batch_size, seq_len] 预测序列
                - targets: [batch_size, seq_len] 实际目标值
                - metrics: Dict[str, float] 评估指标
        """
        try:
            time.time()
            all_predictions = []
            all_targets = []
            all_features = []  # 用于收集所有批次的 features

            # 通过Protocol检查确保类型安全
            if not isinstance(dataloader, TimeSeriesDataLoaderProtocol):
                raise TypeError(
                    f"dataloader必须实现TimeSeriesDataLoaderProtocol接口，"
                    f"当前类型: {type(dataloader)}"
                )

            # 确保数据集类型正确
            if not isinstance(dataloader.dataset, TimeSeriesWindowDataset):
                raise TypeError(
                    f"dataloader.dataset必须是TimeSeriesDataset类型，"
                    f"当前类型: {type(dataloader.dataset)}"
                )

            self.logger.info(
                f"开始批量预测:\n"
                f"- 数据集大小: {len(dataloader.dataset) if hasattr(dataloader.dataset, '__len__') else '未知'}\n"
                f"- 批次大小: {dataloader.batch_size}\n"
                f"- 批次数量: {len(dataloader)}"
            )

            self.model.eval()
            with torch.no_grad(), amp.autocast(enabled=True):
                for batch_idx, batch in enumerate(iter(dataloader)): # 使用 iter() 显式获取迭代器
                    batch_start = time.time()

                    # 确保batch中的数据是tensor类型
                    if not isinstance(batch['features'], Tensor):
                        raise TypeError(f"预期features是Tensor类型，实际是{type(batch['features'])}")
                    if not isinstance(batch['target'], Tensor):
                        raise TypeError(f"预期target是Tensor类型，实际是{type(batch['target'])}")

                    # 转移到设备
                    features = batch['features'].to(self.device)
                    targets = batch['target'].to(self.device)
                    all_features.append(features)  # 将当前批次的 features 添加到列表

                    # 预测（启用缓存）
                    result = self.predict_sequence(
                        features=features,
                        return_confidence=False,
                        batch_size=None,
                        use_cache=True
                    )
                    predictions = result.get('predictions')

                    # 收集结果
                    all_predictions.append(predictions)
                    all_targets.append(targets)

                    batch_time = time.time() - batch_start
                    if (batch_idx + 1) % 10 == 0:  # 每10个批次记录一次进度
                        self.logger.debug(
                            f"批次进度: {batch_idx + 1}/{len(dataloader)}\n"
                            f"- 批次用时: {batch_time:.2f}秒"
                        )

            # 合并结果
            predictions = torch.cat(all_predictions, dim=0)
            targets = torch.cat(all_targets, dim=0)
            features = torch.cat(all_features, dim=0)  # 新增：合并所有批次的 features

            # 记录标准化状态
            self._log_standardization_status(dataloader.dataset)

            # 评估预测结果 (标准化状态由数据集自动维护)
            metrics = self.evaluator.evaluate(
                model=self.model,
                batch={'features': features, 'target': targets}
            )

            return {
                'predictions': predictions,
                'targets': targets,
                'metrics': metrics
            }

        except Exception as e:
            self.logger.error(f"批量预测失败: {e!s}")
            raise
