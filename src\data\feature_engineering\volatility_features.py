"""波动特征生成器 - 计算时序波动特征

此模块负责计算时序数据的波动特征，使用GARCH模型捕捉时间序列的条件异方差性。
"""

from typing import Literal, cast

import numpy as np
import torch

from src.data.feature_engineering.base import BaseFeatureGenerator
from src.utils.config.manager import ConfigManager
from src.utils.logger import get_logger

# 定义有效的模型类型和分布类型
ModelType = Literal['GARCH', 'ARCH', 'EGARCH', 'FIGARCH', 'APARCH', 'HARCH']
DistType = Literal['normal', 'gaussian', 't', 'studentst', 'skewstudent', 'skewt', 'ged', 'generalized error']


class VolatilityFeatureGenerator(BaseFeatureGenerator):
    """波动特征生成器

    计算时序数据的波动特征
    """

    def __init__(self, config: ConfigManager):
        """初始化波动特征生成器

        Args:
            config: 配置管理器实例
        """
        if not isinstance(config, ConfigManager):
            raise TypeError("config参数必须为ConfigManager实例")

        self.config = config
        self.logger = get_logger(__name__)
        self.feature_names: list[str] = []
        self._feature_count = 0

        # 添加类型注解的类属性
        self._enabled: bool = False
        self._model_type: ModelType
        self._p: int
        self._q: int
        self._dist: DistType
        self._scale_threshold: float

        # 初始化配置
        self._check_config()

    def _check_config(self) -> None:
        """检查依赖项 (arch 包)。
        假设 ConfigLoader 已处理配置加载和基本验证。
        如果 arch 包未安装，则抛出 ImportError。
        """
        self._enabled = False # 默认禁用
        # 尝试导入 arch，如果失败则直接抛出 ImportError
        try:
            import importlib.util
            if importlib.util.find_spec("arch") is not None:
                self._enabled = True
                self.logger.info("arch 包可用，波动特征生成器已启用。")
            else:
                error_msg = "arch 包未安装，波动特征生成器无法使用。请执行: pip install arch"
                self.logger.error(error_msg)
                raise ImportError(error_msg)
        except ImportError as e:
            error_msg = "arch 包未安装，波动特征生成器无法使用。请执行: pip install arch"
            self.logger.error(error_msg)
            # 不捕获，让 ImportError 向上抛出
            raise ImportError(error_msg) from e

    def generate(self, data: torch.Tensor, **kwargs) -> torch.Tensor:
        """生成波动特征

        Args:
            data: 输入数据张量 [n_samples, n_features]
            **kwargs: 其他参数，必须包含 'model_type', 'p', 'q', 'dist', 'scale_threshold'

        Returns:
            torch.Tensor: 生成的波动特征张量 [n_samples, n_features]

        Raises:
            ValueError: 如果未提供所有必需的参数或参数无效，或计算失败
            ImportError: 如果 arch 包未安装
        """
        # 检查生成器是否启用 (基于全局配置和arch包检查)
        if not self.is_enabled:
            self.logger.info("波动特征生成器(全局)已禁用或arch包不可用，跳过。")
            return torch.empty((data.shape[0], 0), dtype=data.dtype, device=data.device)

        # Roo-Fix: 强制要求通过 kwargs 提供所有参数
        required_params = ['model_type', 'p', 'q', 'dist', 'scale_threshold']
        missing_params = [param for param in required_params if param not in kwargs]
        if missing_params:
            error_msg = f"调用 generate 时缺少必需的参数: {', '.join(missing_params)}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # 从 kwargs 获取参数
        model_type_to_use = kwargs['model_type']
        p_to_use = kwargs['p']
        q_to_use = kwargs['q']
        dist_to_use = kwargs['dist']
        try:
            # 尝试将 scale_threshold 转换为 float
            scale_threshold_to_use = float(kwargs['scale_threshold'])
        except (ValueError, TypeError) as e:
            error_msg = f"无效的 scale_threshold (来源: kwargs): {kwargs['scale_threshold']}，必须为数值类型"
            self.logger.error(error_msg)
            raise ValueError(error_msg) from e

        source = "kwargs" # 明确来源

        # --- 参数验证 (对将要使用的参数进行验证) ---
        valid_model_types = {'GARCH', 'ARCH', 'EGARCH', 'FIGARCH', 'APARCH', 'HARCH'}
        valid_dist_types = {'normal', 'gaussian', 't', 'studentst', 'skewstudent', 'skewt', 'ged', 'generalized error'}

        if not isinstance(model_type_to_use, str) or model_type_to_use not in valid_model_types:
            error_msg = f"无效的 model_type (来源: {source}): {model_type_to_use}. 可选: {valid_model_types}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        if not isinstance(p_to_use, int) or p_to_use <= 0:
            error_msg = f"无效的 p (来源: {source}): {p_to_use}，必须为正整数"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        if not isinstance(q_to_use, int) or q_to_use <= 0:
            error_msg = f"无效的 q (来源: {source}): {q_to_use}，必须为正整数"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        if not isinstance(dist_to_use, str) or dist_to_use not in valid_dist_types:
            error_msg = f"无效的 dist (来源: {source}): {dist_to_use}. 可选: {valid_dist_types}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        if not isinstance(scale_threshold_to_use, float) or scale_threshold_to_use <= 0:
            error_msg = f"无效的 scale_threshold (来源: {source}): {scale_threshold_to_use}，必须为正浮点数"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        # --- 参数验证结束 ---

        self.logger.info(f"开始生成波动特征 (使用 {source} 的参数)")
        self.logger.info(
            f"参数详情 - 类型: {model_type_to_use}, p: {p_to_use}, q: {q_to_use}, "
            f"分布: {dist_to_use}, 缩放阈值: {scale_threshold_to_use}"
        )
        self.logger.debug(
            f"VolatilityFeatureGenerator - 输入数据统计:\n"
            f"- 形状: {data.shape}\n"
            f"- 数据类型: {data.dtype}\n"
            f"- 设备: {data.device}\n"
            f"- 均值(每列): {data.mean(dim=0).tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 标准差(每列): {data.std(dim=0).tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 最小值(每列): {data.min(dim=0).values.tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 最大值(每列): {data.max(dim=0).values.tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 是否包含NaN: {torch.isnan(data).any().item()}\n"
            f"- 是否包含Inf: {torch.isinf(data).any().item()}"
        )

        # 移除外层 try...except，让具体错误抛出
        if torch.isinf(data).any():
            error_msg = "输入数据包含无穷大值，无法进行波动率特征计算。"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # 导入 arch 包 (已在 _check_config 中检查，但为安全起见再次导入)
        # 如果这里失败，ImportError 会被抛出
        from arch import arch_model

        n_samples, n_features = data.shape
        volatility = torch.zeros_like(data)
        current_feature_names = [] # 使用局部变量，成功后再更新实例变量

        for i in range(n_features):
            # 原始数据统计
            orig_series = data[:, i].cpu().numpy()
            orig_mean = np.mean(orig_series)
            orig_std = np.std(orig_series)

            # 检查标准差是否在有效范围内
            if orig_std <= 1e-6: # 使用一个小的阈值避免除零或数值不稳定
                error_msg = f"特征 #{i} 标准差过小 ({orig_std:.4f})，无法计算波动特征。"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
                # 不再填充0或添加错误后缀，直接抛出异常

            # 根据标准差大小计算缩放因子
            scale_factor = 1.0
            if orig_std < 1.0:
                # 对于小于1的标准差，使用缩放因子将其放大到接近1
                scale_factor = 1.0 / orig_std
            elif orig_std > scale_threshold_to_use:
                # 对于超过阈值的标准差，使用缩放因子将其缩小到阈值
                scale_factor = scale_threshold_to_use / orig_std
                self.logger.debug(f"特征 #{i} 标准差({orig_std:.2f})超过阈值({scale_threshold_to_use})，应用缩放因子: {scale_factor:.4f}")

            # 缩放数据
            scaled_series = orig_series * scale_factor
            self.logger.debug(
                f"特征 #{i} 预处理 - 原始均值: {orig_mean:.2f}, 原始标准差: {orig_std:.2f}\n"
                f"应用缩放因子: {scale_factor}, 缩放后标准差: {np.std(scaled_series):.2f}"
            )

            # 初始化GARCH模型 - 移除 try...except，让初始化错误抛出
            self.logger.debug(f"Feature #{i}: Initializing arch_model with type={model_type_to_use}, p={p_to_use}, q={q_to_use}, dist={dist_to_use}")
            am = arch_model(
                scaled_series,
                vol=cast(ModelType, model_type_to_use),
                p=p_to_use,
                q=q_to_use,
                dist=cast(DistType, dist_to_use),
                rescale=False # 我们手动缩放
            )
            self.logger.debug(f"Feature #{i}: arch_model initialized. Fitting model...")
            # 拟合模型 - 移除 try...except，让拟合错误抛出
            res = am.fit(update_freq=0, disp='off')
            self.logger.debug(f"Feature #{i}: Model fitting complete. Convergence: {res.summary().tables[0].data[5][1] if res.summary() else 'N/A'}")


            # 获取条件波动率并恢复原始尺度
            # 检查 res.conditional_volatility 是否有效
            if res.conditional_volatility is None or len(res.conditional_volatility) != n_samples:
                error_msg = f"特征 #{i}: GARCH模型拟合后未返回有效的条件波动率。"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
                # 不再填充NaN或添加错误后缀

            cond_vol = np.array(res.conditional_volatility) / float(scale_factor)

            # 检查恢复尺度后是否有无效值 (NaN 或 Inf)
            if np.any(np.isnan(cond_vol)) or np.any(np.isinf(cond_vol)):
                error_msg = f"特征 #{i}: 计算出的条件波动率包含 NaN 或 Inf。"
                self.logger.error(error_msg)
                raise ValueError(error_msg) # 抛出异常而不是替换

            volatility[:, i] = torch.from_numpy(cond_vol).to(data.device)

            # 仅在成功时更新特征名称
            current_feature_names.append(f"volatility_feature{i}")

        # 循环成功结束后，更新实例变量
        self.feature_names = current_feature_names
        self._feature_count = len(current_feature_names)


        # 计算有效值的范围用于日志记录 (此时不应有NaN/Inf)
        valid_mask = ~torch.isnan(volatility) # 理论上应该全是 True
        min_val_str = "N/A"
        max_val_str = "N/A"
        if valid_mask.any():
            valid_volatility = volatility[valid_mask]
            # 再次检查过滤后是否还有有效值（理论上应该有，除非输入全是NaN）
            if valid_volatility.numel() > 0:
                min_val_str = f"{valid_volatility.min().item():.2f}"
                max_val_str = f"{valid_volatility.max().item():.2f}"

        self.logger.info(
            f"成功计算波动聚集特征 | 维度: {volatility.shape}\n"
            f"- 已自动调整输入数据尺度\n"
            f"- 输出波动率范围 (有效值): [{min_val_str}, {max_val_str}]"
        )
        # 返回计算好的波动率张量
        return volatility

    # 移除外层 except 块
    # except Exception as e:
        #     error_msg = f"波动聚集特征计算失败: {str(e)}"
        #     self.logger.error(error_msg)
        #     raise RuntimeError(error_msg) from e

    def get_feature_names(self) -> list[str]:
        """获取生成的特征名称列表

        Returns:
            List[str]: 特征名称列表
        """
        # 确保在 generate 未成功执行时返回空列表，而不是潜在的旧状态
        if hasattr(self, 'feature_names') and isinstance(self.feature_names, list):
            return self.feature_names
        return []

    @property
    def feature_count(self) -> int:
        """获取生成的特征数量

        Returns:
            int: 特征数量
        """
        # 确保在 generate 未成功执行时返回0
        if hasattr(self, '_feature_count') and isinstance(self._feature_count, int):
            return self._feature_count
        return 0

    @property
    def is_enabled(self) -> bool:
        """检查特征生成器是否启用

        Returns:
            bool: 是否启用
        """
        # _enabled 在 _check_config 中设置
        return self._enabled
