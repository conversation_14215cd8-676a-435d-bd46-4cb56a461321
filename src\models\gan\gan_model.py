"""时序生成对抗网络模型 - 基于GAN的时间序列生成框架

项目结构模块索引：
1. 基础设施模块:
   - src/utils/config_manager.py: 配置管理，GAN参数控制
   - src/utils/logger.py: 日志系统，训练过程记录
   - src/utils/cuda_manager.py: 系统监控，性能追踪
   - src/utils/path_utils.py: 路径工具，模型存储管理
   - src/utils/resource_manager.py: 资源管理，内存优化
   - src/utils/exception_handler.py: 异常处理，错误恢复
   - src/utils/cuda_manager.py: GPU管理，计算资源调度

2. 共用模块:
   - src/data/data_loader.py: 数据加载，时序数据处理
   - src/data/time_series_dataset.py: 时序数据集，数据组织
   - src/data/metrics_calculator.py: 指标计算，模型评估

3. 配置文件:
   - config.yaml:
     ├── gan:
     │   ├── generator:
     │   │   ├── noise_dim: 噪声维度
     │   │   ├── hidden_dim: 隐藏维度
     │   │   └── layers: 网络层配置
     │   ├── discriminator:
     │   │   ├── hidden_dim: 隐藏维度
     │   │   ├── layers: 网络层配置
     │   │   └── dropout: 丢弃率
     │   └── training:
     │       ├── gp_weight: 梯度惩罚权重
     │       ├── feat_matching: 特征匹配配置
     │       └── consistency: 一致性配置
     └── optimization:
         ├── g_optimizer: 生成器优化器
         └── d_optimizer: 判别器优化器

4. 父类模块:
   - src/models/base/base_model.py: BaseModel，模型基类
   - src/models/base/base_module.py: BaseModule，基础功能模块
   - src/models/base/model_state.py: ModelState，状态管理器
   - src/models/base/model_saver.py: ModelSaver，存储管理器

5. 同阶段GAN模块:
   - generator.py: 时序生成器实现
   - discriminator.py: 时序判别器实现
   - loss_calculator.py: 损失计算实现
   - trainer.py: GAN训练器实现
   - feature_matching.py: 特征匹配实现
   - positional_encoding.py: 位置编码实现

核心功能：
1. GAN架构管理
   - 生成器-判别器集成
   - 组件间协调
   - 模型状态管理
   - 噪声生成控制

2. 对抗训练流程
   - 生成器训练步骤
   - 判别器训练步骤
   - 梯度惩罚计算
   - 数值稳定性保障

3. 时序样本生成
   - 条件生成机制
   - 批量生成优化
   - 样本质量控制
   - 特征匹配增强

4. 高级训练功能
   - 混合精度训练
   - 梯度累积支持
   - 动态批次大小
   - 自适应学习率

5. 模型状态管理
   - 检查点保存恢复
   - 训练状态追踪
   - 模式切换(训练/评估)
   - 配置动态更新
"""

from __future__ import annotations

import traceback
from pathlib import Path
from typing import Any

import torch
from torch.amp.autocast_mode import autocast  # 保留autocast导入
from torch.utils.checkpoint import checkpoint  # 正确导入checkpoint

from src.models.base.base_model import BaseModel

# 移除 DimensionAdapter 导入
# cuda_manager已在顶部导入
from src.models.gan.discriminator import TimeSeriesDiscriminator
from src.models.gan.generator import TimeSeriesGenerator
from src.models.gan.loss_calculator import LossCalculator
from src.models.gan.noise_manager import NoiseConfig, NoiseManager

# 导入混合精度管理模块
from src.utils.amp_manager import get_amp_manager
from src.utils.config_manager import ConfigManager
from src.utils.cuda import cuda_manager

# 导入新的utils模块功能


class GANModel(BaseModel):
    """时序GAN模型

    作为统一的接口层，负责：
    1. 提供所有公共接口
    2. 封装内部组件细节
    3. 统一参数和返回值
    4. 状态管理
    """

    def __init__(self, config: ConfigManager, feature_dim: int | None = None, window_size: int | None = None):
        """初始化GAN模型

        Args:
            config (ConfigManager): 配置管理器
            feature_dim: 可选特征维度
            window_size: 滑动窗口大小，从数据流水线获取
        """
        super().__init__(config)
        self.generator_optimizer = None
        self.discriminator_optimizer = None
        self.config = config.model
        # feature_dim 的最终确定和验证在 L164-182 进行，此处移除默认值 20
        self.feature_dim = feature_dim or self.config_manager.get('data.feature_dim')

        # 移除 DimensionAdapter 初始化

        # 内存优化配置 - 从training.checkpoint中获取，强制要求存在
        if not hasattr(config, 'training') or not hasattr(config.training, 'checkpoint'):
             raise ValueError("配置缺少 training.checkpoint 部分")
        if not hasattr(config.training.checkpoint, 'memory_optimization'):
             raise ValueError("配置缺少 training.checkpoint.memory_optimization 参数")
        # 断言 checkpoint 不为 None 且 memory_optimization 存在且为 bool
        assert config.training.checkpoint is not None and isinstance(getattr(config.training.checkpoint, 'memory_optimization', None), bool), \
            "training.checkpoint.memory_optimization 必须存在且为布尔值"
        self.memory_optimization = config.training.checkpoint.memory_optimization

        if not hasattr(config, 'prediction') or not hasattr(config.prediction, 'batch_size'):
            raise ValueError("配置缺少必要参数: prediction.batch_size")
        self.prediction_batch_size = config.prediction.batch_size

        if not hasattr(config.training.checkpoint, 'enable_checkpointing'):
            raise ValueError("配置缺少 training.checkpoint.enable_checkpointing 参数")
        # 断言 checkpoint 不为 None 且 enable_checkpointing 存在且为 bool
        assert config.training.checkpoint is not None and isinstance(getattr(config.training.checkpoint, 'enable_checkpointing', None), bool), \
            "training.checkpoint.enable_checkpointing 必须存在且为布尔值"
        self.use_gradient_checkpointing = config.training.checkpoint.enable_checkpointing

        # 记录内存优化配置
        self._logger.info(f"内存优化配置:\n- 内存优化: {self.memory_optimization}\n- 预测批次大小: {self.prediction_batch_size}\n- 梯度检查点: {self.use_gradient_checkpointing}")

        # 模型保存路径配置 - 强制要求在 training 配置中指定
        if not hasattr(self.config_manager.training, 'save_dir'):
            # This should ideally be caught by TrainingConfig's __post_init__ if save_dir is mandatory
            raise ValueError("配置缺少 training.save_dir 参数")

        save_dir_from_config = self.config_manager.training.save_dir

        # TrainingConfig ensures save_dir is a Path object if correctly loaded.
        # The assert here should check for Path type.
        assert save_dir_from_config is not None and isinstance(save_dir_from_config, Path), \
            "training.save_dir 必须是一个有效的 Path 对象且不为 None"

        self.save_dir = save_dir_from_config # Already a Path object
        if not self.save_dir.exists():
            self.save_dir.mkdir(parents=True, exist_ok=True)
        self._logger.info(f"模型保存路径: {self.save_dir.absolute()}")
        try:
            # 1. 基础配置(直接访问配置属性)
            model_config = config.model
            training_config = config.training # Added to access training specific configs
            data_config = config.data

            # 特征维度处理 - 支持动态特征维度
            total_input_dim = None  # 初始化变量
            if feature_dim is not None:
                # 显式指定特征维度(已经排除了目标列)
                self.initial_feature_dim = feature_dim
                if not isinstance(self.initial_feature_dim, int) or self.initial_feature_dim <= 0:
                    raise ValueError(f"无效的特征维度: {self.initial_feature_dim}，必须为正整数")
                total_input_dim = self.initial_feature_dim + 1  # 更新总输入维度
            elif hasattr(data_config, 'feature_dim') and data_config.feature_dim is not None:
                # 从配置中获取特征维度(已经排除了目标列)
                self.initial_feature_dim = data_config.feature_dim
                if not isinstance(self.initial_feature_dim, int) or self.initial_feature_dim <= 0:
                    raise ValueError(f"配置中的特征维度无效: {self.initial_feature_dim}，必须为正整数")
                total_input_dim = self.initial_feature_dim + 1  # 更新总输入维度
            else:
                # 使用默认值，将在运行时动态确定
                self._logger.info("特征维度未指定，将在运行时动态确定")
                self.initial_feature_dim = None
            # 设置当前特征维度
            self.current_feature_dim = self.initial_feature_dim
            # 从配置中获取维度参数
            if not hasattr(model_config, 'hidden_dim'):
                raise ValueError("配置缺少必要参数: model.hidden_dim")
            if not hasattr(model_config, 'noise_dim'):
                raise ValueError("配置缺少必要参数: model.noise_dim")

            # 确保hidden_dim和noise_dim有效 (L184/L186已检查存在性，此处移除默认值)
            # 断言 hidden_dim 不为 None 且为 int
            assert model_config.hidden_dim is not None and isinstance(model_config.hidden_dim, int), "model.hidden_dim 必须存在且为整数"
            self.hidden_dim = int(model_config.hidden_dim)
            # 断言 noise_dim 不为 None 且为 int
            assert model_config.noise_dim is not None and isinstance(model_config.noise_dim, int), "model.noise_dim 必须存在且为整数"
            self.noise_dim = int(model_config.noise_dim)
            if self.hidden_dim <= 0 or self.noise_dim <= 0:
                raise ValueError(f"hidden_dim和noise_dim必须为正整数, 当前值: hidden_dim={self.hidden_dim}, noise_dim={self.noise_dim}")
            self._logger.debug(f"模型参数初始化 - hidden_dim:{self.hidden_dim} noise_dim:{self.noise_dim}")

            # 初始化噪声生成器
            # 检查是否有噪声配置
            # 确保 model_config.noise 存在且是 NoiseParamsConfig 类型 (由ConfigLoader保证)
            if not hasattr(model_config, 'noise'):
                raise ValueError("模型配置中缺少 'noise' 部分。")

            noise_params_config = model_config.noise
            self._logger.info(f"从配置加载噪声参数: {noise_params_config}")

            # 处理 dtype 转换
            dtype_str = noise_params_config.dtype
            try:
                # 移除可选的 "torch." 前缀
                if dtype_str.startswith("torch."):
                    dtype_str = dtype_str[len("torch."):]
                torch_dtype = getattr(torch, dtype_str)
            except AttributeError:
                self._logger.error(f"无效的 torch.dtype 字符串: {noise_params_config.dtype}")
                raise ValueError(f"无效的 torch.dtype 字符串: {noise_params_config.dtype}")

            noise_config = NoiseConfig(
                dim=noise_params_config.dim,
                distribution=noise_params_config.distribution,
                scale=noise_params_config.scale,
                seed=noise_params_config.seed,
                dtype=torch_dtype,
                structured=noise_params_config.structured,
                temporal_correlation=noise_params_config.temporal_correlation,
                feature_correlation=noise_params_config.feature_correlation,
                noise_patterns=noise_params_config.noise_patterns
            )
            self.noise_manager = NoiseManager(noise_config)

            # 2. 初始化生成器
            self._logger.info(f"开始初始化生成器，特征维度: {self.feature_dim}...")
            # 使用当前特征维度初始化生成器，支持None值
            self.generator = TimeSeriesGenerator(
                config,
                feature_dim=self.current_feature_dim  # 使用current_feature_dim而不是feature_dim
            ).to(self.device)

            # 3. 初始化判别器 - 支持动态特征维度
            if self.initial_feature_dim is not None:
                total_input_dim = self.initial_feature_dim + 1  # 特征+目标
                self._logger.info(f"开始初始化判别器，总输入维度: {total_input_dim}...")
            else:
                self._logger.info("开始初始化判别器，特征维度将在运行时动态确定...")

            # 使用Optional[int]类型的condition_feature_dim
            self.discriminator = TimeSeriesDiscriminator(
                target_dim=1,
                condition_feature_dim=self.current_feature_dim,  # 使用动态特征维度
                hidden_dim=self.hidden_dim,
                config=self.config_manager
            ).to(self.device)

            if self.initial_feature_dim is not None:
                self._logger.info(f"判别器初始化完成 - 总输入维度: {total_input_dim}")
            else:
                self._logger.info("判别器初始化完成 - 特征维度将在运行时动态确定")

            # 3.5 初始化损失计算器
            self._logger.info("开始初始化损失计算器...")
            self.loss_calculator = LossCalculator(config, discriminator=self.discriminator)

            # 4. 优化器将在Trainer中初始化并设置

            # 5. 配置混合精度训练 (GAN需要独立的 Scaler)
            self.lambda_gp = config.training.lambda_gp
            # n_critic 将由 Trainer 动态传入，此处不再从配置读取

            # 初始化混合精度管理器
            # 强制要求 training.mixed_precision 配置
            if not hasattr(config, 'training'):
                raise ValueError("配置缺少 training 部分")
            if not hasattr(config.training, 'mixed_precision'):
                raise ValueError("配置缺少必要参数: training.mixed_precision")
            if config.training.mixed_precision is None:
                raise ValueError("training.mixed_precision 不能为None")
            mixed_precision_config = config.training.mixed_precision

            # 创建生成器和判别器的混合精度管理器
            self.generator_amp = get_amp_manager("generator", mixed_precision_config)
            self.discriminator_amp = get_amp_manager("discriminator", mixed_precision_config)

            # 设置混合精度状态
            self.use_amp = self.generator_amp.enabled

            # 为了兼容性，设置 scaler 属性
            self.generator_scaler = self.generator_amp.scaler
            self.discriminator_scaler = self.discriminator_amp.scaler

            self._logger.info(
                f"GAN混合精度训练初始化完成:\n"
                f"- 启用状态: {'启用' if self.use_amp else '禁用'}\n"
                f"- 生成器AMP管理器: {self.generator_amp.name}\n"
                f"- 判别器AMP管理器: {self.discriminator_amp.name}"
            )

            # 6. 读取梯度裁剪值
            if not hasattr(training_config, 'gradient_clip_val'):
                raise ValueError("配置缺少 training.gradient_clip_val 参数")
            self.gradient_clip_val = training_config.gradient_clip_val
            if not isinstance(self.gradient_clip_val, float | int) or self.gradient_clip_val <= 0:
                raise ValueError(f"training.gradient_clip_val 必须是正数，得到 {self.gradient_clip_val}")
            self._logger.info(f"梯度裁剪值从配置加载: {self.gradient_clip_val}")

            # 7. 初始化LazyModules参数
            self._logger.info("初始化模型参数...")
            with torch.no_grad():
                # 优先使用传入的window_size参数
                if window_size is not None:
                    actual_window_size = window_size
                else:
                    # 从配置中获取window_size
                    try:
                        # 调试日志：打印完整配置
                        self._logger.debug(f"完整配置内容: {self.config_manager.to_dict()}")

                        # 确保config_manager有data属性
                        if not hasattr(self.config_manager, 'data'):
                            raise AttributeError("配置缺少data节")

                        # 获取data配置
                        data_config = self.config_manager.data
                        self._logger.debug(f"data配置内容: {data_config.to_dict() if hasattr(data_config, 'to_dict') else str(data_config)}")

                        # 确保data配置有window_size属性
                        if not hasattr(data_config, 'window_size'):
                            raise AttributeError("data节缺少window_size参数")

                        actual_window_size = data_config.window_size
                        self._logger.info(f"从配置获取的window_size: {actual_window_size}")

                        # 验证window_size值
                        if not isinstance(actual_window_size, int) or actual_window_size <= 0:
                            raise ValueError(f"无效的window_size值: {actual_window_size}，必须为正整数")

                    except Exception as e:
                        # 获取可读的配置内容
                        config_content = self.config_manager.to_dict() if hasattr(self.config_manager, 'to_dict') else str(self.config_manager)
                        error_msg = (
                            f"无法获取有效的window_size配置\n"
                            f"错误: {e!s}\n"
                            f"请确保在config.yaml的data节下配置了有效的window_size参数\n"
                            f"当前配置内容: {config_content}"
                        )
                        self._logger.error(error_msg)
                        raise ValueError(error_msg)

                self._logger.info(f"使用窗口大小: {actual_window_size}")

                # 生成器初始化
                with torch.device(self.device):
                    dummy_noise = torch.randn(1, actual_window_size, self.noise_dim)

            # 如果特征维度未知，使用默认值进行初始化
            default_feature_dim = 15  # 定义默认特征维度
            if self.initial_feature_dim is None:
                raise ValueError("特征维度未指定。请在配置中提供 initial_feature_dim 或通过参数指定")
            else:
                dummy_features = torch.randn(1, actual_window_size, self.initial_feature_dim)

            # Ensure generator is on the same device
            self.generator = self.generator.to(self.device)
            # Ensure inputs are on the correct device for initialization call
            _ = self.generator(dummy_features.to(self.device), dummy_noise.to(self.device))

            # 判别器初始化
            dummy_target = torch.randn(1, actual_window_size, 1)

            # 如果特征维度未知，使用默认值进行初始化 (Prepare features again for discriminator)
            if self.initial_feature_dim is None:
                # Reusing dummy_features variable name as in original code
                dummy_features = torch.randn(1, actual_window_size, default_feature_dim)
            else:
                # Reusing dummy_features variable name as in original code
                dummy_features = torch.randn(1, actual_window_size, self.initial_feature_dim)

            # Ensure discriminator is on the same device
            self.discriminator = self.discriminator.to(self.device)

            self._logger.debug(f"初始化判别器测试输入 - target: {dummy_target.shape}, features: {dummy_features.shape}")
            # Ensure inputs are on the correct device for initialization call
            _ = self.discriminator(dummy_target.to(self.device), dummy_features.to(self.device))
            self._logger.debug("判别器初始化测试通过")

            self._logger.info(f"GAN模型初始化完成 - 参数: G({self.generator.count_parameters():,}) D({self.discriminator.count_parameters():,})")

        except Exception as e:
            error_msg = f"GAN模型初始化失败: {e!s}\n{traceback.format_exc()}"
            self._logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    def configure_noise(self, **kwargs):
        """配置噪声生成参数

        Args:
            **kwargs: 配置参数
                - distribution: 分布类型 ('normal' 或 'uniform')
                - scale: 缩放因子
                - seed: 随机种子
                - dtype: 数据类型
                - dim: 噪声维度
        """
        self.noise_manager.configure(**kwargs)

    def generate_noise(
        self,
        batch_size: int,
        seq_length: int,
        return_info: bool = False
    ) -> torch.Tensor | tuple[torch.Tensor, dict]:
        """生成噪声张量

        Args:
            batch_size: 批次大小
            seq_length: 序列长度
            return_info: 是否返回噪声信息

        Returns:
            Union[torch.Tensor, Tuple[torch.Tensor, Dict]]:
                - 噪声张量 [batch_size, seq_length, noise_dim]
                - 可选的噪声信息字典
        """
        return self.noise_manager.generate(batch_size, seq_length, return_info)

    def validate_noise(self, input_noise: torch.Tensor) -> None:
        """验证噪声张量

        Args:
            input_noise: 输入噪声张量

        Raises:
            TypeError: 当输入不是张量类型时
            ValueError: 当噪声数据类型不正确时
        """
        # 检查类型
        if not isinstance(input_noise, torch.Tensor):
            raise TypeError(f"噪声类型错误: 期望 torch.Tensor，实际为 {type(input_noise)}")

        # 检查数据类型
        if input_noise.dtype == torch.bool:
            raise ValueError("噪声不能是布尔类型，请提供浮点类型噪声")

        # 使用噪声生成器的验证方法
        if not self.noise_manager.validate(input_noise):
            raise ValueError("噪声验证失败，不符合噪声管理器的要求")

    # 使用基类的validate_input/output方法
# 移除 _update_discriminator_input_dim 方法，因为它依赖 DimensionAdapter

    def forward(
        self,
        x: torch.Tensor,
        noise: torch.Tensor | None = None,
        is_training: bool = True
    ) -> torch.Tensor:
        """前向传播,支持可选的噪声输入

        Args:
            features: 输入特征 [batch_size, seq_length, feature_dim]
            noise: 可选的噪声输入 [batch_size, seq_length, noise_dim]
            is_training: 是否处于训练模式

        Returns:
            torch.Tensor: 生成的序列
        """
        try:
            # 验证特征输入(简化验证逻辑)
            self.validate_tensor(tensor=x, name="输入特征", expected_dims=3)

            # 移除使用 DimensionAdapter 检测和调整判别器的逻辑
            # Generator 和 Discriminator 内部会在 forward 时自行处理维度变化
            actual_feature_dim = x.size(2) # 直接获取维度
            if actual_feature_dim != self.current_feature_dim:
                 self.logger.info(
                     f"输入特征维度({actual_feature_dim})与当前记录的特征维度({self.current_feature_dim})不同。"
                     f"Generator 和 Discriminator 将在内部处理此变化。"
                 )
                 # 更新记录的维度，但不在此处调整模型结构
                 self.current_feature_dim = actual_feature_dim

            # 如果没有提供噪声,则自动生成
            # 预处理输入特征
            features = x.to(self.device)
            batch_size, seq_length = features.shape[:2]

            # 处理噪声输入
            if noise is None:
                # 生成新的噪声
                generated_noise = self.generate_noise(
                    batch_size=batch_size,
                    seq_length=seq_length,
                    return_info=False
                )
                if isinstance(generated_noise, tuple):
                    generated_noise = generated_noise[0]  # 只取张量部分
                generated_noise = generated_noise.to(self.device) # 将生成的噪声移动到设备
            else:
                # 验证并使用提供的噪声 - 如果验证失败会直接抛出异常
                self.validate_noise(noise)
                generated_noise = noise.to(self.device)

            # 训练/评估模式切换
            if is_training:
                self.generator.train()
            else:
                self.generator.eval()

            # 调用生成器，使用统一的混合精度管理
            # 使用混合精度管理器的autocast上下文
            with self.generator_amp.autocast_context():
                # 如果启用了梯度检查点，使用torch.utils.checkpoint
                if self.use_gradient_checkpointing and is_training and self.generator.training:
                    # 定义一个包装函数，用于梯度检查点
                    def generate_fn(features, noise):
                        return self.generator(condition_features=features, noise=noise)

                    # 使用梯度检查点调用生成器
                    self.logger.debug("使用梯度检查点进行前向传播")
                    raw_outputs = checkpoint(
                        generate_fn,
                        features,
                        generated_noise,
                        use_reentrant=False # 显式设置以消除警告
                    )
                else:
                    # 常规调用生成器
                    raw_outputs = self.generator(
                        condition_features=features,
                        noise=generated_noise
                    )

            # 检查raw_outputs是否为None
            if raw_outputs is None:
                error_msg = "生成器输出为None"
                self.logger.error(error_msg)
                # 抛出异常，而不是返回空张量
                raise RuntimeError(error_msg)

            # 数值稳定性检查 - 如果有问题会直接抛出异常
            self._check_numerical_stability(raw_outputs, "生成器输出")
            processed_outputs = raw_outputs # 直接返回原始输出，移除tanh和缩放

            return processed_outputs

        except Exception as e:
            error_msg = f"前向传播失败: {e!s}\n{traceback.format_exc()}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)


    def discriminate(self, target_sequence: torch.Tensor, condition_features: torch.Tensor) -> torch.Tensor:
        """判别器前向传播

        Args:
            target_sequence: 目标序列
            condition_features: 条件特征

        Returns:
            torch.Tensor: 判别器得分
        """
        # 检查内存使用情况，如果内存使用率高，则主动清理缓存
        if cuda_manager is not None:
            try:
                memory_info = cuda_manager.get_memory_info()
                if memory_info and memory_info.utilization > 0.7:
                    self.logger.debug(
                        f"判别器前向传播前内存使用率较高: {memory_info.utilization:.1%}，主动清理缓存"
                    )
                    cuda_manager.clear_cache()
            except Exception as mem_e:
                self.logger.warning(f"内存检查失败: {mem_e!s}")

        # 强制垃圾回收
        import gc
        gc.collect()

        # 确保输入数据类型一致，避免混合精度问题
        # 在混合精度模式下，强制使用float32进行判别器计算
        with autocast('cuda', enabled=False):
            # 显式转换为float32类型
            target_sequence_float32 = target_sequence.to(torch.float32)
            condition_features_float32 = condition_features.to(torch.float32)

            # 使用float32类型进行判别
            # 调用更新后的 discriminator.forward，传递目标和条件特征
            scores = self.discriminator(target_sequence_float32, condition_features_float32)

            # 检查输出稳定性 (scores is now a single tensor, not a tuple)
            self._check_numerical_stability(scores, "判别器得分")

            # 主动释放不再需要的中间结果
            del target_sequence_float32
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            del condition_features_float32
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            # 强制垃圾回收
            gc.collect()

            return scores

    def train_step(
        self,
        features: torch.Tensor,
        targets: torch.Tensor,
        data_stream: torch.cuda.Stream,
        noise: torch.Tensor | None = None,
        # max_grad_norm: float = 1.0, # 移除参数，将从 self.gradient_clip_val 获取
        n_critic: int = 1, # 接收 n_critic 参数，默认值设为1以防直接调用
        current_lambda_gp: float | None = None # 新增参数：当前的梯度惩罚权重
    ) -> dict[str, float]:
        """执行单步训练（统一的训练接口）

        Args:
            features: 输入特征 (已在data_stream上异步传输)
            targets: 目标序列 (已在data_stream上异步传输)
            data_stream: 用于数据传输的CUDA流
            noise: 可选的噪声输入
            max_grad_norm: 梯度裁剪的最大范数
            n_critic: 判别器更新次数 (由Trainer动态传入)

        Returns:
            Dict[str, float]: 训练指标

        Note:
            此方法使用CUDA流进行并行计算，要求必须提供有效的data_stream
        """
        try:
            # 创建生成器和判别器的计算CUDA流
            generator_stream = cuda_manager.create_stream("generator_compute")
            discriminator_stream = cuda_manager.create_stream("discriminator_compute")

            # 注意: features 和 targets 已经在 data_stream 上异步传输
            # 检查输入的数值稳定性，如果有问题会直接抛出异常
            self._check_numerical_stability(features, "训练输入")
            self._check_numerical_stability(targets, "训练目标")

            # 生成噪声（如果未提供）
            if noise is None:
                noise = torch.randn(
                    features.size(0),
                    features.size(1),  # 序列长度
                    self.noise_dim,
                    device=self.device
                )

            # 在生成器计算流中执行生成器前向传播
            with torch.cuda.stream(generator_stream):
                # 等待数据传输完成
                generator_stream.wait_stream(data_stream)
                self._logger.debug("Generator stream waiting for data stream completed.")

                # 训练生成器
                self.generator.train()
                with self.generator_amp.autocast_context(stream=generator_stream):
                    # 直接调用generator forward生成假数据
                    fake_data_for_g = self.generator(features, noise)
                    # 如果 generator 返回元组，取第一个元素
                    if isinstance(fake_data_for_g, tuple):
                        fake_data_for_g = fake_data_for_g[0]
                fixed_fake_data = self._check_numerical_stability(fake_data_for_g, "生成数据 (用于生成器训练)", fix_tensor=True)
                if fixed_fake_data is not None:
                    fake_data_for_g = fixed_fake_data

                # 训练生成器，传入流
                # MODIFIED: _train_generator_step now returns a dictionary
                g_loss_components_dict = self._train_generator_step(features, targets, fake_data_for_g, stream=generator_stream)
                # Ensure g_loss_components_dict is a dictionary and contains 'total_g_loss'
                if not isinstance(g_loss_components_dict, dict) or 'total_g_loss' not in g_loss_components_dict:
                    self.logger.error(f"从 _train_generator_step 返回的 g_loss_components_dict 格式不正确: {g_loss_components_dict}")
                    raise TypeError("g_loss_components_dict 必须是包含 'total_g_loss'键的字典")
                g_loss_total_tensor = g_loss_components_dict['total_g_loss'] # Extract the main loss tensor

                if not isinstance(g_loss_total_tensor, torch.Tensor):
                    self.logger.error(f"g_loss_components_dict['total_g_loss'] 不是张量: {type(g_loss_total_tensor)}")
                    raise TypeError("total_g_loss 必须是张量")

                if torch.isnan(g_loss_total_tensor) or torch.isinf(g_loss_total_tensor):
                    self.logger.error("生成器总损失在 train_step 中检测到 NaN/Inf")
                    for name, value in g_loss_components_dict.items():
                        # Ensure value is a tensor before calling .item()
                        log_value = value.item() if isinstance(value, torch.Tensor) and value.numel() == 1 else value
                        self.logger.error(f"  - G Component {name}: {log_value}")
                    raise ValueError("生成器总损失出现NaN/Inf")

            # 在判别器流中执行判别器训练
            # MODIFIED: d_loss_components_list to store dicts from each critic step
            d_loss_components_list = []
            d_loss_total_scalar_sum = 0.0 # Sum of total_d_loss scalar values

            if n_critic > 0:
                with torch.cuda.stream(discriminator_stream):
                    # 等待数据传输完成
                    discriminator_stream.wait_stream(data_stream)
                    self._logger.debug("Discriminator stream waiting for data stream completed.")

                    # 确保判别器在训练模式
                    self.discriminator.train()

                    # 使用传入的 n_critic 控制循环次数
                    for critic_step in range(n_critic):
                        # 生成假数据 (每次循环都生成新的假数据)
                        self.generator.eval()  # 生成假数据时，生成器应处于评估模式
                        with torch.no_grad():  # 不需要计算生成器的梯度
                            # 使用 generate 方法生成，确保与推理时一致
                            fake_data_loop = self.generate(features, noise)
                            # 处理元组类型的fake_data
                            if isinstance(fake_data_loop, tuple):
                                fake_data_loop = tuple(t.to(self.device) for t in fake_data_loop)
                            else:
                                fake_data_loop = fake_data_loop.to(self.device)
                            # 检查数值稳定性并准备detached数据
                            if isinstance(fake_data_loop, tuple):
                                for i, tensor_val in enumerate(fake_data_loop): # Renamed tensor to tensor_val
                                    self._check_numerical_stability(tensor_val, f"生成数据[{i}] (D step)")
                                # 判别器只需要第一个输出（通常是序列）
                                fake_detached_loop = fake_data_loop[0].detach()
                            else:
                                self._check_numerical_stability(fake_data_loop, "生成数据 (D step)")
                                fake_detached_loop = fake_data_loop.detach()
                        self.generator.train()  # 恢复生成器训练模式

                        # 训练判别器单步
                        try:
                            # 确保 fake_detached_loop 是正确的格式 (Tensor)
                            if not isinstance(fake_detached_loop, torch.Tensor):
                                raise TypeError(f"判别器输入 fake_data 必须是 Tensor，得到 {type(fake_detached_loop)}")

                            # MODIFIED: _train_discriminator_step now returns a dictionary
                            current_d_loss_components = self._train_discriminator_step(
                                features=features,
                                real_data=targets,  # real_data 就是 targets
                                fake_data=fake_detached_loop,  # 使用当前循环生成的假数据
                                stream=discriminator_stream,  # 传入判别器流
                                current_lambda_gp=current_lambda_gp if current_lambda_gp is not None else self.lambda_gp # 传递 lambda_gp
                            )
                            # Ensure current_d_loss_components is a dictionary and contains 'total_d_loss'
                            if not isinstance(current_d_loss_components, dict) or 'total_d_loss' not in current_d_loss_components:
                                self.logger.error(f"从 _train_discriminator_step 返回的 current_d_loss_components 格式不正确: {current_d_loss_components}")
                                raise TypeError("current_d_loss_components 必须是包含 'total_d_loss'键的字典")

                            d_loss_components_list.append(current_d_loss_components)

                            d_total_loss_step_tensor = current_d_loss_components['total_d_loss']
                            if not isinstance(d_total_loss_step_tensor, torch.Tensor):
                                self.logger.error(f"current_d_loss_components['total_d_loss'] 不是张量: {type(d_total_loss_step_tensor)}")
                                raise TypeError("total_d_loss 必须是张量")

                            if torch.isnan(d_total_loss_step_tensor) or torch.isinf(d_total_loss_step_tensor):
                                error_msg = f"判别器总损失在第 {critic_step+1}/{n_critic} 次更新时出现NaN/Inf"
                                self.logger.error(error_msg)
                                # Log components if available
                                for name, value in current_d_loss_components.items():
                                    log_value = value.item() if isinstance(value, torch.Tensor) and value.numel() == 1 else value
                                    self.logger.error(f"  - D Component {name} (step {critic_step+1}): {log_value}")
                                raise ValueError(error_msg)
                            d_loss_total_scalar_sum += d_total_loss_step_tensor.item()  # 累加损失值

                        except Exception as e:
                            error_msg = f"GAN 判别器训练步骤失败 (第 {critic_step+1}/{n_critic} 次更新): {e!s}"
                            self.logger.error(error_msg)
                            raise RuntimeError(error_msg) from e
            else: # if n_critic == 0
                d_loss_total_scalar_sum = 0.0

            # 同步两个流，确保所有操作完成
            cuda_manager.synchronize_stream(generator_stream)
            cuda_manager.synchronize_stream(discriminator_stream)

            # 避免除零错误
            d_loss_total_scalar_sum / n_critic if n_critic > 0 else 0.0  # 计算平均损失

            # 准备返回的训练指标字典
            metrics = {}

            # 添加G的损失分量 (确保是标量)
            for name, value_tensor in g_loss_components_dict.items():
                if isinstance(value_tensor, torch.Tensor) and value_tensor.numel() == 1:
                    metrics[f'{name}'] = value_tensor.item() # Use original name from dict, e.g. 'total_g_loss', 'g_loss_adv'
                elif isinstance(value_tensor, float | int): # If it's already a scalar
                    metrics[f'{name}'] = value_tensor
                else:
                    self.logger.warning(f"G损失分量 {name} 不是标量张量或数字，无法添加到metrics: {value_tensor}")

            # 添加D的损失分量 (averaged over critic steps, 确保是标量)
            if n_critic > 0 and d_loss_components_list:
                # Initialize a dictionary to sum up component values before averaging
                sum_d_components = dict.fromkeys(d_loss_components_list[0].keys(), 0.0)
                valid_d_components_count = dict.fromkeys(d_loss_components_list[0].keys(), 0)

                for comp_dict in d_loss_components_list:
                    for key, value_tensor in comp_dict.items():
                        if isinstance(value_tensor, torch.Tensor) and value_tensor.numel() == 1:
                            sum_d_components[key] += value_tensor.item()
                            valid_d_components_count[key] +=1
                        elif isinstance(value_tensor, float | int):
                             sum_d_components[key] += value_tensor
                             valid_d_components_count[key] +=1
                        else:
                            self.logger.warning(f"D损失分量 {key} 不是标量张量或数字，无法用于平均: {value_tensor}")

                for key in sum_d_components:
                    if valid_d_components_count[key] > 0:
                        metrics[f'{key}'] = sum_d_components[key] / valid_d_components_count[key] # Use original name, e.g. 'total_d_loss'
                    else: # Should not happen if d_loss_components_list is not empty
                        metrics[f'{key}'] = 0.0
            elif n_critic == 0 : # if discriminator is not trained, report 0 for its losses
                # This assumes d_loss_components_list would have keys like 'total_d_loss', 'd_loss_real' etc.
                # We need a way to know these keys if d_loss_components_list is empty.
                # For now, let's assume we know the keys or handle it if the list is empty.
                # Placeholder: if d_loss_components_list is empty, we might not have D loss keys.
                # A better way would be to initialize D loss keys to 0 if n_critic is 0.
                metrics['total_d_loss'] = 0.0
                metrics['d_loss_real'] = 0.0
                metrics['d_loss_fake'] = 0.0
                metrics['d_loss_gp'] = 0.0

            # 添加 GANTrainer 期望的键
            if 'total_g_loss' in metrics:
                metrics['g_loss'] = metrics['total_g_loss']
            if 'total_d_loss' in metrics:
                metrics['d_loss'] = metrics['total_d_loss']

            # 将判别器梯度范数添加到最终指标中
            # d_grad_norm 是在 d_loss_components_list[0] (如果 n_critic > 0) 中计算的
            if n_critic > 0 and d_loss_components_list and 'd_grad_norm' in d_loss_components_list[0]:
                # 如果有多次判别器步骤，可以考虑平均梯度范数，或只取第一个/最后一个
                # 此处简单取第一个判别器步骤的梯度范数
                avg_d_grad_norm = sum(comp.get('d_grad_norm', torch.tensor(0.0)).item() for comp in d_loss_components_list if 'd_grad_norm' in comp) / len(d_loss_components_list)
                metrics['d_grad_norm'] = avg_d_grad_norm
            elif 'd_grad_norm' in metrics: # If already present from a single step (n_critic=0 but somehow d_metrics had it)
                pass # Already there
            else:
                metrics['d_grad_norm'] = 0.0 # Default if not calculated


            return metrics

        except Exception as e:
            self._logger.error(f"训练步骤失败: {e!s}\n{traceback.format_exc()}")
            raise RuntimeError(f"训练步骤失败: {e!s}\n{traceback.format_exc()}")

    def _train_discriminator_step(
        self,
        features: torch.Tensor,
        real_data: torch.Tensor,
        fake_data: torch.Tensor,
        stream: Any,
        current_lambda_gp: float # 新增参数
    ) -> dict[str, torch.Tensor]: # MODIFIED: Changed return type annotation
        """训练判别器的单步

        Args:
            features: 条件特征
            real_data: 真实数据
            fake_data: 生成的数据
            stream: CUDA流，必须指定

        Returns:
            torch.Tensor: 判别器损失值

        Raises:
            ValueError: 当损失计算出现NaN/Inf (错误码:5202)
            AssertionError: 当优化器状态异常 (错误码:5202)
            RuntimeError: 当发生CUDA内存溢出 (错误码:5202)
        """
        self.discriminator.train()
        # 安全地调用zero_grad
        if self.discriminator_optimizer is not None:
            self.discriminator_optimizer.zero_grad()

        # 主动检查内存使用情况，如果接近阈值则清理缓存并重置峰值统计
        if cuda_manager is not None:
            try:
                memory_info = cuda_manager.get_memory_info()
                # 降低阈值从0.85到0.75，更积极地预防OOM
                if memory_info and memory_info.utilization > 0.75:
                    self.logger.warning(
                        f"判别器训练前内存使用率过高: {memory_info.utilization:.1%}，主动清理缓存并重置峰值统计"
                    )
                    cuda_manager.clear_cache()
                    # 重置峰值内存统计，帮助更准确地监控后续内存使用
                    if torch.cuda.is_available():
                        torch.cuda.reset_peak_memory_stats()
                        self.logger.info("已重置CUDA峰值内存统计")

                    # 主动释放不需要的中间变量
                    import gc
                    gc.collect()
                    self.logger.info("已执行Python垃圾回收")
            except Exception as mem_e:
                self.logger.warning(f"内存检查失败: {mem_e!s}")

        try: # <<<< NEW MAIN TRY BLOCK FOR THE METHOD
            # 使用判别器的混合精度管理器，必须指定流
            with self.discriminator_amp.autocast_context(stream=stream):
                # 首先验证所有数据
                # 1. 验证张量格式和数值
                self.validate_tensor(real_data, "真实数据")
                self.validate_tensor(fake_data, "生成数据")
                self.validate_tensor(features, "条件特征")

                # 2. 检查设备一致性
                self._check_device_consistency(real_data, fake_data, features)

                # 3. 检查形状兼容性 - 确保3D输入 [batch, seq_len, features]
                expected_shape = (real_data.size(0), real_data.size(1), real_data.size(2))
                self._check_shape_compatibility(
                    real_data, fake_data,
                    expected_shapes=[expected_shape, expected_shape]
                )
                self._check_numerical_stability(real_data, "真实数据")
                self._check_numerical_stability(fake_data, "生成数据")

                # 计算判别器损失
                # 始终使用float32进行判别器计算，确保数值稳定性和梯度计算一致性
                real_data_float32 = real_data.to(torch.float32)
                fake_data_float32 = fake_data.to(torch.float32)
                features_float32 = features.to(torch.float32)

                # 使用统一的float32类型进行判别
                # 暂时禁用混合精度，确保判别器计算的精度一致性
                try:
                    with autocast('cuda', enabled=False):
                        # 1. 先计算真实样本得分，避免同时使用显存
                        real_scores = self.discriminate(real_data_float32, features_float32)

                        # 2. 再计算虚假样本得分
                        fake_scores = self.discriminate(fake_data_float32, features_float32)

                        # 主动清理缓存，减少内存碎片化
                        if torch.cuda.is_available():
                            torch.cuda.empty_cache()
                except RuntimeError as e:
                    if "CUDA out of memory" in str(e):
                        # 捕获OOM异常，清理缓存并重新抛出
                        self.logger.error(f"判别器前向传播时发生CUDA内存溢出: {e!s}")
                        if torch.cuda.is_available():
                            torch.cuda.empty_cache()
                        raise RuntimeError(f"判别器前向传播时发生CUDA内存溢出: {e!s}")
                    else:
                        # 其他RuntimeError，直接重新抛出
                        raise

            # 基础数值检查
            self._check_numerical_stability(real_scores, "判别器真实得分")
            self._check_numerical_stability(fake_scores, "判别器生成得分")

            # 强制垃圾回收
            import gc
            gc.collect()

            # 分开计算真实样本和虚假样本的损失，避免同时使用显存
            try:
                # 1. 计算真实样本损失
                d_loss_real = -torch.mean(real_scores)
                real_loss_value = d_loss_real.item()  # 保存数值用于日志记录

                # 显式删除不再需要的中间结果
                del real_scores
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

                # 强制垃圾回收
                gc.collect()

                # 2. 计算虚假样本损失
                d_loss_fake = torch.mean(fake_scores)
                fake_loss_value = d_loss_fake.item()  # 保存数值用于日志记录

                # 显式删除不再需要的中间结果
                del fake_scores
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

                # 强制垃圾回收
                gc.collect()

                # 3. 计算梯度惩罚
                # 检查内存使用情况，如果内存使用率高，则使用更小的批次大小计算梯度惩罚
                if cuda_manager is not None:
                    try:
                        memory_info = cuda_manager.get_memory_info()
                        if memory_info and memory_info.utilization > 0.7:
                            self.logger.warning(
                                f"梯度惩罚计算前内存使用率较高: {memory_info.utilization:.1%}，主动清理缓存"
                            )
                            cuda_manager.clear_cache()
                    except Exception as mem_e:
                        self.logger.warning(f"内存检查失败: {mem_e!s}")

                # 计算梯度惩罚
                gradient_penalty = self.loss_calculator._compute_gradient_penalty(
                    real_data_float32,
                    fake_data_float32,
                    features_float32
                )
                gp_value = gradient_penalty.item()  # 保存数值用于日志记录

                # 显式删除不再需要的中间结果
                del real_data_float32, fake_data_float32
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

                # 强制垃圾回收
                gc.collect()

                # 4. 组合损失
                d_loss = d_loss_real + d_loss_fake + current_lambda_gp * gradient_penalty

                # 记录损失分解
                self.logger.debug(
                    f"判别器损失分解:\n"
                    f"- 真实损失 (d_loss_real): {real_loss_value:.4f}\n"
                    f"- 生成损失 (d_loss_fake): {fake_loss_value:.4f}\n"
                    f"- 梯度惩罚 (d_loss_gp): {gp_value:.4f} (权重: {current_lambda_gp})\n" # Added weight logging
                    f"- 总损失 (total_d_loss): {d_loss.item():.4f}" # Use d_loss which is total_d_loss
                )
            except RuntimeError as e:
                if "CUDA out of memory" in str(e):
                    self.logger.error(f"损失计算时发生CUDA内存溢出: {e!s}")
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                    raise RuntimeError(f"损失计算时发生CUDA内存溢出: {e!s}")
                else:
                    raise
        except Exception as e:
            self.logger.error(f"判别器损失计算失败: {e!s}")
            raise

        # 检查损失值
        if torch.isnan(d_loss) or torch.isinf(d_loss):
            self.logger.error("判别器总损失计算出现NaN/Inf (错误码:5202)")
            # Log individual components to help debug
            self.logger.error(f"  d_loss_real: {d_loss_real.item()}, d_loss_fake: {d_loss_fake.item()}, d_loss_gp: {gradient_penalty.item()}")
            raise ValueError("判别器总损失计算出现NaN/Inf")

        # 构建返回字典
        d_losses_dict = {
            'total_d_loss': d_loss,
            'd_loss_real': d_loss_real,
            'd_loss_fake': d_loss_fake,
            'd_loss_gp': gradient_penalty # gradient_penalty is already a tensor
        }

        # 梯度计算和优化
        if self.discriminator_optimizer is not None:
            try:
                self.discriminator_optimizer.zero_grad()
                # 使用总损失进行反向传播
                total_d_loss_for_backward = d_losses_dict['total_d_loss']

                if self.use_amp:
                    try:
                        with self.discriminator_amp.autocast_context():
                            self.discriminator_scaler.scale(total_d_loss_for_backward).backward()
                    except RuntimeError as e:
                        if "CUDA out of memory" in str(e):
                            self.logger.error(f"反向传播时发生CUDA内存溢出: {e!s}")
                            if torch.cuda.is_available():
                                torch.cuda.empty_cache()
                            raise
                        elif "Function 'BmmBackward0' returned nan values" in str(e):
                            self.logger.error(f"混合精度训练中发生BmmBackward0 NaN错误: {e!s}, 尝试非混合精度.")
                            try:
                                total_d_loss_for_backward.backward()
                            except Exception as fallback_e:
                                self.logger.error(f"非混合精度模式反向传播也失败: {fallback_e!s}")
                                raise RuntimeError(f"判别器训练失败: {e!s}") from fallback_e
                        else:
                            raise

                    # 在混合精度训练中取消缩放并应用梯度裁剪
                    self.discriminator_scaler.unscale_(self.discriminator_optimizer) # 在裁剪前取消缩放
                    torch.nn.utils.clip_grad_norm_(self.discriminator.parameters(), self.gradient_clip_val) # 裁剪梯度

                    # NaN/Inf 梯度检查
                    nan_inf_params = []
                    for name, param in self.discriminator.named_parameters():
                        if param.grad is not None and (torch.isnan(param.grad).any() or torch.isinf(param.grad).any()):
                            nan_inf_params.append(name)
                    if nan_inf_params:
                        error_msg = f"AMP: 判别器参数 {nan_inf_params} 的梯度包含NaN/Inf，抛出异常以暴露问题"
                        self.logger.error(error_msg)
                        raise ValueError(error_msg)

                    # 计算梯度范数 (在优化器步骤之前)
                    d_grad_norm = 0.0
                    for p in self.discriminator.parameters():
                        if p.grad is not None:
                            d_grad_norm += p.grad.data.norm(2).item() ** 2
                    d_grad_norm = d_grad_norm ** 0.5
                    # 添加梯度范数到损失组件字典
                    d_losses_dict['d_grad_norm'] = torch.tensor(d_grad_norm, device=d_loss.device)

                    self.logger.debug(f"判别器梯度范数: {d_grad_norm:.4f}")

                    try:
                        self.discriminator_amp.step_optimizer(self.discriminator_optimizer, stream=stream)
                        self.discriminator_amp.update_scaler(stream=stream)
                    except RuntimeError as e:
                        if "CUDA out of memory" in str(e):
                            self.logger.error(f"优化器步骤时发生CUDA内存溢出: {e!s}")
                            if torch.cuda.is_available():
                                torch.cuda.empty_cache()
                            raise
                        elif "unscale_" in str(e) and ("inf" in str(e).lower() or "nan" in str(e).lower()):
                            self.logger.error(f"混合精度unscale_操作失败 (NaN/Inf梯度): {e!s}")
                            self.logger.warning("跳过当前优化器步骤.")
                        else:
                            raise
                else: # Not use_amp
                    try:
                        total_d_loss_for_backward.backward()
                    except RuntimeError as e:
                        if "CUDA out of memory" in str(e):
                            self.logger.error(f"反向传播时发生CUDA内存溢出: {e!s}")
                            if torch.cuda.is_available():
                                torch.cuda.empty_cache()
                            raise
                        elif "Function 'BmmBackward0' returned nan values" in str(e):
                            self.logger.error(f"反向传播时发生BmmBackward0 NaN错误: {e!s}")
                            raise
                        else:
                            raise

                    # 应用梯度裁剪
                    torch.nn.utils.clip_grad_norm_(self.discriminator.parameters(), self.gradient_clip_val) # 裁剪梯度

                    # NaN/Inf 梯度检查和替换
                    nan_params = []
                    for name, param in self.discriminator.named_parameters():
                        if param.grad is not None and (torch.isnan(param.grad).any() or torch.isinf(param.grad).any()):
                            nan_params.append(name)
                            param.grad = torch.zeros_like(param.grad)
                    if nan_params:
                         self.logger.warning(f"判别器梯度中发现inf/nan，已替换为零: {nan_params}")

                    # 计算梯度范数 (在优化器步骤之前)
                    d_grad_norm = 0.0
                    for p in self.discriminator.parameters():
                        if p.grad is not None:
                            d_grad_norm += p.grad.data.norm(2).item() ** 2
                    d_grad_norm = d_grad_norm ** 0.5
                    # 添加梯度范数到损失组件字典
                    d_losses_dict['d_grad_norm'] = torch.tensor(d_grad_norm, device=d_loss.device)

                    self.logger.debug(f"判别器梯度范数: {d_grad_norm:.4f}")

                    try:
                        self.discriminator_optimizer.step()
                    except RuntimeError as e:
                        if "CUDA out of memory" in str(e):
                            self.logger.error(f"优化器步骤时发生CUDA内存溢出: {e!s}")
                            if torch.cuda.is_available():
                                torch.cuda.empty_cache()
                            raise
                        else:
                            raise
            except RuntimeError as e: # Catch errors from optimizer step or zero_grad
                if "CUDA out of memory" in str(e):
                    self.logger.error(f"判别器优化器步骤失败 (CUDA内存溢出): {e!s}")
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                    raise
                else:
                    self.logger.error(f"判别器优化器步骤失败 (错误码:5202): {e!s}")
                    raise

        return d_losses_dict # MODIFIED: Return the dictionary


    def _train_generator_step(
        self,
        features: torch.Tensor,
        targets: torch.Tensor, # 添加 targets 参数
        fake_data: torch.Tensor,
        stream: Any
    ) -> dict[str, torch.Tensor]: # MODIFIED: Changed return type annotation
        """训练生成器的单步（内部方法）

        Args:
            features: 条件特征
            targets: 目标序列
            fake_data: 生成的假数据
            stream: CUDA流，必须指定

        Returns:
            Dict[str, torch.Tensor]: 包含总损失和各分量损失的字典
        """
        # --- 添加日志辅助函数 ---
        def log_tensor_stats(logger, tensor, name):
            if tensor is None:
                logger.debug(f"Tensor '{name}' is None.")
                return
            if not isinstance(tensor, torch.Tensor):
                logger.debug(f"'{name}' is not a tensor, type: {type(tensor)}")
                return
            try:
                stats = {
                    "shape": tuple(tensor.shape),
                    "dtype": tensor.dtype,
                    "min": tensor.min().item() if tensor.numel() > 0 else 'N/A',
                    "max": tensor.max().item() if tensor.numel() > 0 else 'N/A',
                    "mean": tensor.float().mean().item() if tensor.numel() > 0 else 'N/A',
                    "has_nan": torch.isnan(tensor).any().item(),
                    "has_inf": torch.isinf(tensor).any().item(),
                    "device": tensor.device
                }
                logger.debug(f"Stats for '{name}': {stats}")
            except Exception as e:
                logger.error(f"Error getting stats for tensor '{name}': {e}")
        # --- 结束日志辅助函数 ---

        self.generator.train()
        # 安全地调用zero_grad
        if self.generator_optimizer is not None:
            self.generator_optimizer.zero_grad()

        # 使用生成器的混合精度管理器，必须指定流
        with self.generator_amp.autocast_context(stream=stream):
            # 记录 fake_data 统计信息
            log_tensor_stats(self.logger, fake_data, "_train_generator_step: fake_data (before discriminate)")

            # 1. 判别器评分
            fake_scores = self.discriminate(fake_data, features)
            fixed_scores = self._check_numerical_stability(fake_scores, "生成器判别得分", fix_tensor=True)
            if fixed_scores is not None:
                fake_scores = fixed_scores

            # 记录 fake_scores 统计信息
            log_tensor_stats(self.logger, fake_scores, "_train_generator_step: fake_scores (before loss calculation)")

            # 2. 计算损失
            # LossCalculator.compute_generator_loss 现在返回一个字典
            g_loss_components_dict = self.loss_calculator.compute_generator_loss(
                fake_sequence=fake_data,
                real_sequence=targets, # 修正：使用 targets
                fake_scores=fake_scores,
                condition_features=features  # 传递条件特征
            )

            # 从字典中提取总损失用于反向传播和检查
            if not isinstance(g_loss_components_dict, dict) or 'total_g_loss' not in g_loss_components_dict:
                self.logger.error(f"compute_generator_loss 返回的格式不正确: {g_loss_components_dict}")
                raise TypeError("compute_generator_loss 必须返回包含 'total_g_loss' 键的字典")

            g_loss_total_tensor = g_loss_components_dict['total_g_loss']
            if not isinstance(g_loss_total_tensor, torch.Tensor):
                self.logger.error(f"g_loss_components_dict['total_g_loss'] 不是张量: {type(g_loss_total_tensor)}")
                raise TypeError("total_g_loss 必须是张量")

            # 3. 检查损失值并记录状态
            if torch.isnan(g_loss_total_tensor) or torch.isinf(g_loss_total_tensor):
                self.logger.error("生成器总损失计算出现NaN/Inf (错误码:5201)")
                for name, value in g_loss_components_dict.items(): # Log all components from the dict
                    log_value = value.item() if isinstance(value, torch.Tensor) and value.numel() == 1 else value
                    self.logger.error(f"  - G Component {name}: {log_value}")
                raise ValueError("生成器总损失计算出现NaN/Inf")

        # 4. 处理梯度计算异常 (此部分移到优化器步骤内部，以确保损失值已在autocast外部)
        # try-except for requires_grad check can remain, but backward/step is more complex

        # 执行优化步骤前检查梯度有效性
        if self.generator_optimizer is not None:
            try:
                self.generator_optimizer.zero_grad() # Moved here, standard practice

                # 使用混合精度管理器的缩放和步骤方法
                if self.use_amp:
                    try:
                        g_loss_float32 = g_loss_total_tensor.to(torch.float32)
                        if torch.isnan(g_loss_float32) or torch.isinf(g_loss_float32):
                            self.logger.warning("生成器总损失(float32)包含NaN/Inf，使用小的非零值替代")
                            g_loss_float32 = torch.tensor(1e-8, device=g_loss_total_tensor.device, dtype=torch.float32)

                        self.generator_scaler.scale(g_loss_float32).backward()

                        # 梯度检查 (在 unscale/step 之前)
                        self.generator_scaler.unscale_(self.generator_optimizer) # 在裁剪前取消缩放
                        torch.nn.utils.clip_grad_norm_(self.generator.parameters(), self.gradient_clip_val) # 裁剪梯度

                        # 检查梯度有效性 (AMP)
                        nan_inf_params = []
                        for name, param in self.generator.named_parameters():
                            if param.grad is not None:
                                param.grad = param.grad.to(torch.float32) # Ensure grad is float32
                                if not torch.isfinite(param.grad).all():
                                    nan_inf_params.append(name)

                        if nan_inf_params:
                            error_msg = f"AMP: 生成器参数 {nan_inf_params} 的梯度包含NaN/Inf，抛出异常以暴露问题"
                            self.logger.error(error_msg)
                            raise ValueError(error_msg)

                        # --- 更详细的梯度检查和日志 ---
                        max_grad_val = 0.0
                        problematic_layers = []
                        for name, param in self.generator.named_parameters():
                            if param.grad is not None:
                                grad_float = param.grad.data.float()
                                if not torch.isfinite(grad_float).all():
                                     problematic_layers.append(name)
                                     self.logger.error(f"  检测到 NaN/Inf 梯度在层: {name}")
                                     # 可选：将 NaN/Inf 梯度替换为零以尝试继续
                                     # param.grad.data = torch.nan_to_num(param.grad.data, nan=0.0, posinf=0.0, neginf=0.0)
                                else:
                                    current_max = grad_float.abs().max().item()
                                    max_grad_val = max(max_grad_val, current_max)
                        if problematic_layers:
                             # 如果仍然决定抛出异常，可以在这里执行
                             error_msg_detail = f"检测到 NaN/Inf 梯度在以下层: {problematic_layers}"
                             self.logger.error(error_msg_detail)
                             # raise ValueError(error_msg_detail) # 取消注释以在发现NaN/Inf时停止

                        self.logger.debug(f"生成器梯度检查完成。最大梯度绝对值: {max_grad_val:.4e}. 问题层: {problematic_layers if problematic_layers else '无'}")
                        # --- 结束详细梯度检查 ---

                        # 计算梯度范数 (在优化器步骤之前)
                        g_grad_norm = 0.0
                        for name, param in self.generator.named_parameters(): # 使用 named_parameters()
                            if param.grad is not None:
                                grad_norm_val = param.grad.data.norm(2).item()
                                if not torch.isfinite(torch.tensor(grad_norm_val)):
                                    self.logger.warning(f"层 {name} 的梯度范数计算结果为 NaN/Inf，跳过此层范数累加") # 使用 name
                                    continue
                                g_grad_norm += grad_norm_val ** 2
                        g_grad_norm = g_grad_norm ** 0.5
                        # 添加梯度范数到损失组件字典
                        g_loss_components_dict['g_grad_norm'] = torch.tensor(g_grad_norm, device=g_loss_total_tensor.device)

                        self.logger.debug(f"生成器梯度范数 (可能已修正): {g_grad_norm:.4f}")

                        self.generator_amp.step_optimizer(self.generator_optimizer, stream=stream) # scaler.step()
                        self.generator_amp.update_scaler(stream=stream) # scaler.update()
                    except Exception as e:
                        self.logger.error(f"生成器混合精度训练步骤失败: {e!s}")
                        # Fallback or re-raise, for now, re-raise to make issue visible
                        raise
                else: # Not use_amp
                    try:
                        g_loss_float32 = g_loss_total_tensor.to(torch.float32)
                        if torch.isnan(g_loss_float32) or torch.isinf(g_loss_float32):
                            self.logger.warning("生成器总损失(float32)包含NaN/Inf，使用小的非零值替代")
                            g_loss_float32 = torch.tensor(1e-8, device=g_loss_total_tensor.device, dtype=torch.float32)

                        g_loss_float32.backward() # Removed retain_graph=True unless necessary

                        torch.nn.utils.clip_grad_norm_(self.generator.parameters(), self.gradient_clip_val) # 裁剪梯度

                        # 检查梯度有效性 (非AMP)
                        nan_inf_params = []
                        for name, param in self.generator.named_parameters():
                            if param.grad is not None:
                                param.grad = param.grad.to(torch.float32)
                                if not torch.isfinite(param.grad).all():
                                    nan_inf_params.append(name)

                        if nan_inf_params:
                            error_msg = f"非AMP: 生成器参数 {nan_inf_params} 的梯度包含NaN/Inf，抛出异常以暴露问题"
                            self.logger.error(error_msg)
                            raise ValueError(error_msg)

                        # --- 更详细的梯度检查和日志 ---
                        max_grad_val = 0.0
                        problematic_layers = []
                        for name, param in self.generator.named_parameters():
                            if param.grad is not None:
                                grad_float = param.grad.data.float()
                                if not torch.isfinite(grad_float).all():
                                     problematic_layers.append(name)
                                     self.logger.error(f"  检测到 NaN/Inf 梯度在层: {name}")
                                     # 可选：将 NaN/Inf 梯度替换为零以尝试继续
                                     # param.grad.data = torch.nan_to_num(param.grad.data, nan=0.0, posinf=0.0, neginf=0.0)
                                else:
                                    current_max = grad_float.abs().max().item()
                                    max_grad_val = max(max_grad_val, current_max)
                        if problematic_layers:
                             # 如果仍然决定抛出异常，可以在这里执行
                             error_msg_detail = f"检测到 NaN/Inf 梯度在以下层: {problematic_layers}"
                             self.logger.error(error_msg_detail)
                             # raise ValueError(error_msg_detail) # 取消注释以在发现NaN/Inf时停止

                        self.logger.debug(f"生成器梯度检查完成。最大梯度绝对值: {max_grad_val:.4e}. 问题层: {problematic_layers if problematic_layers else '无'}")
                        # --- 结束详细梯度检查 ---

                        # 计算梯度范数 (在优化器步骤之前)
                        g_grad_norm = 0.0
                        for name, param in self.generator.named_parameters(): # 使用 named_parameters()
                            if param.grad is not None:
                                grad_norm_val = param.grad.data.norm(2).item()
                                if not torch.isfinite(torch.tensor(grad_norm_val)):
                                    self.logger.warning(f"层 {name} 的梯度范数计算结果为 NaN/Inf，跳过此层范数累加") # 使用 name
                                    continue
                                g_grad_norm += grad_norm_val ** 2
                        g_grad_norm = g_grad_norm ** 0.5
                        # 添加梯度范数到损失组件字典
                        g_loss_components_dict['g_grad_norm'] = torch.tensor(g_grad_norm, device=g_loss_total_tensor.device)

                        self.logger.debug(f"生成器梯度范数 (可能已修正): {g_grad_norm:.4f}")

                        # if not valid_gradients: # This logic might be too strict if we zero out grads
                        #     self.logger.warning("生成器梯度无效，跳过优化器步骤")
                        # else:
                        #     self.generator_optimizer.step()
                        self.generator_optimizer.step()

                    except Exception as e:
                        self.logger.error(f"生成器普通训练步骤失败: {e!s}")
                        raise
            except Exception as e: # Catch errors from optimizer_G.zero_grad() or the step logic
                self.logger.error(f"生成器优化器处理失败: {e!s}")
                raise RuntimeError(f"生成器优化器处理失败: {e!s}")

        return g_loss_components_dict # MODIFIED: Return the entire dictionary

    def set_optimizers(self, g_optimizer, d_optimizer):
        """设置优化器

        Args:
            g_optimizer: 生成器优化器
            d_optimizer: 判别器优化器
        """
        self.generator_optimizer = g_optimizer
        self.discriminator_optimizer = d_optimizer
        self._logger.info("优化器设置完成")

    def get_state(self) -> dict[str, Any]:
        """获取模型状态"""
        return {
            'generator_state': self.generator.state_dict(),
            'discriminator_state': self.discriminator.state_dict(),
            'config': vars(self.config),
            'current_feature_dim': self.current_feature_dim,  # 保存当前特征维度
            'initial_feature_dim': self.initial_feature_dim,  # 保存初始特征维度
            'generator_optimizer_state': self.generator_optimizer.state_dict() if self.generator_optimizer else None,
            'discriminator_optimizer_state': self.discriminator_optimizer.state_dict() if self.discriminator_optimizer else None
        }

    def set_state(self, state: dict[str, Any]):
        """设置模型状态"""
        if not isinstance(state, dict):
            raise ValueError("状态必须是字典类型")

        # 先恢复特征维度信息，确保模型结构正确
        if 'current_feature_dim' in state:
            new_feature_dim = state['current_feature_dim']

            # 如果特征维度发生变化，使用维度适配器更新模型结构
            if new_feature_dim != self.current_feature_dim:
                self.current_feature_dim = new_feature_dim
                self._logger.info(f"恢复特征维度: {self.current_feature_dim}")

                # 移除调用 DimensionAdapter 的逻辑
                # 判别器将在其 forward 方法中自行处理维度更新
                # 需要确保判别器的 _update_feature_dim 被正确调用
                # （在判别器的 forward 方法开始处检查维度变化）
                self.logger.info("维度已从状态恢复，判别器将在下次前向传播时根据需要更新其内部结构。")

        if 'initial_feature_dim' in state:
            self.initial_feature_dim = state['initial_feature_dim']
            self._logger.info(f"恢复初始特征维度: {self.initial_feature_dim}")

        # 然后加载模型参数
        self.generator.load_state_dict(state['generator_state'])
        self.discriminator.load_state_dict(state['discriminator_state'])

        self._logger.info(f"模型状态加载完成 - 特征维度: {self.current_feature_dim}")

    def train(self, mode: bool = True):
        """设置训练模式"""
        super().train(mode)
        self.generator.train(mode)
        self.discriminator.train(mode)
        return self

    def eval(self):
        """设置评估模式"""
        super().eval()
        self.generator.eval()
        self.discriminator.eval()
        return self

    def log_module_info(self):
        """记录模块信息"""
        try:
            info = (
                f"\n模块信息 - {self.name}:\n"
                f"- 参数数量: {self.count_parameters():,}\n"
                f"- 设备: {next(self.parameters()).device if any(True for _ in self.parameters()) else 'N/A'}\n"
                f"- 主要子模块:"
            )
            for name, module in self.named_children():
                info += f"\n  - {name}: {module.__class__.__name__}"
            self._logger.info(info)
        except Exception as e:
            self.handle_error_with_options(e, "模块信息记录失败", raise_error=False)

    def collect_model_info(self) -> dict[str, Any]:
        """收集模型信息

        Returns:
            Dict[str, Any]: 包含以下信息的字典:
                - total_params: 总参数量
                - trainable_params: 可训练参数量
                - model_type: 模型类型
                - gpu_memory_allocated: 当前显存占用(MB)
                - gpu_memory_peak: 显存峰值(MB)
                - module_devices: 各子模块的设备信息
        """
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)

        return {
            'total_params': total_params,
            'trainable_params': trainable_params,
            'model_type': type(self).__name__,
            'gpu_memory_allocated': f"{torch.cuda.memory_allocated()/1024**2:.1f}MB",
            'gpu_memory_peak': f"{torch.cuda.max_memory_allocated()/1024**2:.1f}MB",
            'module_devices': {
                'generator': next(self.generator.parameters()).device,
                'discriminator': next(self.discriminator.parameters()).device,
                'noise_manager': next(self.generator.parameters()).device,  # 使用生成器的设备代替noise_manager的device
                'gan_trainer': self.device
            }
        }


    def _check_numerical_stability(self, tensor: torch.Tensor, name: str, fix_tensor: bool = False) -> None:
        """检查数值稳定性，如果发现NaN或Inf值则直接抛出异常

        Args:
            tensor: 要检查的张量
            name: 张量名称(用于错误消息)
            fix_tensor: 是否修复问题值 (为了保持接口兼容性，但实际不再使用)

        Raises:
            ValueError: 当张量包含NaN或Inf值时
        """
        # 确保使用float32类型进行检查，避免混合精度问题
        tensor_float32 = tensor.to(torch.float32)

        if torch.isnan(tensor_float32).any() or torch.isinf(tensor_float32).any():
            raise ValueError(f"{name} 包含NaN或Inf值")

        return None

    def generate(
        self,
        features: torch.Tensor,
        noise: torch.Tensor | None = None,
        num_samples: int = 1,
        return_noise: bool = False,
        batch_size: int | None = None
    ) -> torch.Tensor | tuple[torch.Tensor, torch.Tensor]:
        """生成时间序列样本

        Args:
            features: 输入特征 [batch_size, seq_length, feature_dim]
            noise: 可选的噪声输入
            num_samples: 每个特征生成的样本数量
            return_noise: 是否返回使用的噪声
            batch_size: 批处理大小

        Returns:
            生成的样本或(样本, 噪声)元组
        """
        import time
        from datetime import datetime

        start_time = time.time()
        self._logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始生成样本 - 特征形状: {features.shape}")

        try:
            # 检查数值稳定性 - 如果有问题会直接抛出异常
            check_start = time.time()
            self._check_numerical_stability(features, "输入特征")
            check_end = time.time()
            self._logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 数值稳定性检查完成 - 耗时: {check_end - check_start:.2f}秒")

            # 计算批次大小
            total_samples = features.size(0) * num_samples
            # 确保 batch_size 是整数
            actual_batch_size = batch_size if batch_size is not None else self.prediction_batch_size
            if actual_batch_size <= 0:
                actual_batch_size = total_samples
            self._logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 使用批次大小: {actual_batch_size}, 总样本数: {total_samples}")

            samples, noises = [], []
            # 使用确保是整数的 actual_batch_size
            for i in range(0, total_samples, actual_batch_size):
                batch_start = time.time()
                current_batch = min(actual_batch_size, total_samples - i)
                self._logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 处理批次 {i//actual_batch_size + 1}/{(total_samples + actual_batch_size - 1)//actual_batch_size} - 大小: {current_batch}")

                # 准备噪声
                noise_start = time.time()
                if noise is None:
                    self._logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始生成噪声")
                    generated_noise_output = self.noise_manager.generate(
                        batch_size=current_batch,
                        seq_length=features.size(1)
                        # return_info 默认为 False，所以通常返回 Tensor
                    )
                    if isinstance(generated_noise_output, tuple):
                        current_noise = generated_noise_output[0] # 只取张量部分
                    else:
                        current_noise = generated_noise_output
                    self._logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 噪声生成完成 - 形状: {current_noise.shape}, 耗时: {time.time() - noise_start:.2f}秒")
                else:
                    # 外部传入的 noise 已经是 Tensor | None
                    current_noise = noise[i:i+current_batch]
                    self._logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 使用外部噪声 - 形状: {current_noise.shape}")

                # 准备特征
                feature_start = time.time()
                batch_idx = (i // num_samples) % features.size(0)
                current_features = features[batch_idx:batch_idx+1].expand(current_batch, -1, -1)
                self._logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 特征准备完成 - 形状: {current_features.shape}, 耗时: {time.time() - feature_start:.2f}秒")

                # 生成样本
                forward_start = time.time()
                self._logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始前向传播生成样本")
                with torch.no_grad():
                    # 调用 self.forward 进行生成
                    current_samples = self.forward(
                        x=current_features, # forward 使用 x 作为特征参数名
                        noise=current_noise,
                        is_training=False # 生成时总是评估模式
                    )
                self._logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 前向传播完成 - 形状: {current_samples.shape}, 耗时: {time.time() - forward_start:.2f}秒")

                samples.append(current_samples)
                if return_noise:
                    noises.append(current_noise)

                self._logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 批次处理完成 - 耗时: {time.time() - batch_start:.2f}秒")

            # 合并结果
            merge_start = time.time()
            self._logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始合并结果")
            all_samples = torch.cat(samples, dim=0)
            if num_samples > 1:
                all_samples = all_samples.view(features.size(0), num_samples, *all_samples.shape[1:])
            self._logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 结果合并完成 - 耗时: {time.time() - merge_start:.2f}秒")

            end_time = time.time()
            self._logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 生成完成 - 形状: {all_samples.shape}, 总耗时: {end_time - start_time:.2f}秒")

            # 检查内存使用情况
            if torch.cuda.is_available():
                mem_allocated = torch.cuda.memory_allocated() / (1024 ** 2)
                mem_reserved = torch.cuda.memory_reserved() / (1024 ** 2)
                self._logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] CUDA内存使用情况 - 已分配: {mem_allocated:.2f}MB, 已保留: {mem_reserved:.2f}MB")

            return (all_samples, torch.cat(noises, dim=0)) if return_noise else all_samples

        except Exception as e:
            self._logger.error(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 生成失败: {e!s}")
            raise
