"""LightGBM特征重要性详细测试模块

本模块专门测试LightGBM特征重要性的各个方面，包括：
1. 不同类型的特征重要性（split vs gain）
2. 特征重要性的稳定性
3. 特征重要性与实际预测能力的关系
4. 特征重要性在不同数据集大小下的表现
5. 特征重要性对异常值的敏感性

相关模块:
1. 被测试模块:
   - src/data/preprocessing/feature_selector.py: 特征选择器实现中的LightGBM特征重要性评估
2. 依赖模块:
   - lightgbm: LightGBM模型库
"""

import lightgbm as lgb
import numpy as np
import pandas as pd
import pytest
from sklearn.metrics import mean_squared_error
from sklearn.model_selection import train_test_split

from src.data.preprocessing.feature_selector import FeatureSelector


def create_synthetic_dataset(n_samples=1000, n_features=10, n_informative=3, noise=0.1, random_state=42):
    """
    创建合成数据集，具有已知的特征重要性

    Args:
        n_samples: 样本数量
        n_features: 特征总数
        n_informative: 有信息量的特征数量
        noise: 噪声水平
        random_state: 随机种子

    Returns:
        X: 特征数据框
        y: 目标变量
    """
    np.random.seed(random_state)

    # 创建特征矩阵
    X = np.random.randn(n_samples, n_features)

    # 创建特征名称
    feature_names = [f'feature_{i}' for i in range(n_features)]

    # 创建目标变量，只与前n_informative个特征相关
    coefficients = np.zeros(n_features)
    coefficients[:n_informative] = np.linspace(1.0, 0.5, n_informative)  # 递减的系数

    y = np.dot(X, coefficients) + np.random.normal(0, noise, n_samples)

    return pd.DataFrame(X, columns=feature_names), y


@pytest.fixture
def synthetic_dataset():
    """创建用于测试的合成数据集"""
    X, y = create_synthetic_dataset(n_samples=1000, n_features=10, n_informative=3)
    return X, y


def test_feature_importance_types(synthetic_dataset):
    """测试不同类型的特征重要性（split vs gain）"""
    X, y = synthetic_dataset

    # 训练LightGBM模型
    model = lgb.LGBMRegressor(**FeatureSelector.DEFAULT_LGBM_PARAMS)
    model.fit(X, y)

    # 获取不同类型的特征重要性
    split_importance = model.booster_.feature_importance(importance_type='split')
    gain_importance = model.booster_.feature_importance(importance_type='gain')

    # 转换为Series并归一化
    split_importance = pd.Series(split_importance, index=X.columns) / split_importance.sum()
    gain_importance = pd.Series(gain_importance, index=X.columns) / gain_importance.sum()

    # 获取排序后的特征
    split_top_features = split_importance.sort_values(ascending=False).index.tolist()
    gain_top_features = gain_importance.sort_values(ascending=False).index.tolist()

    # 验证两种重要性类型的前3个特征是否相同（可能顺序不同）
    assert set(split_top_features[:3]) == set(gain_top_features[:3]), "两种重要性类型的前3个特征应该相同"

    # 验证前3个特征是否是有信息量的特征
    informative_features = [f'feature_{i}' for i in range(3)]
    assert set(split_top_features[:3]) == set(informative_features), "前3个特征应该是有信息量的特征"


def test_feature_importance_stability():
    """测试特征重要性的稳定性（多次运行结果是否一致）"""
    # 创建数据集
    X, y = create_synthetic_dataset(n_samples=1000, n_features=10, n_informative=3)

    # 多次运行，收集特征重要性
    n_runs = 5
    top_features_list = []

    for i in range(n_runs):
        # 每次使用不同的随机种子
        model = lgb.LGBMRegressor(**FeatureSelector.DEFAULT_LGBM_PARAMS, random_state=i)
        model.fit(X, y)

        # 获取特征重要性
        importance = pd.Series(model.feature_importances_, index=X.columns)
        importance = importance / importance.sum()

        # 获取前3个特征
        top_features = importance.sort_values(ascending=False).index.tolist()[:3]
        top_features_list.append(set(top_features))

    # 计算稳定性分数（前3个特征的一致性）
    stability_score = 0
    for i in range(n_runs):
        for j in range(i+1, n_runs):
            # 计算两次运行的前3个特征的交集大小
            intersection = len(top_features_list[i].intersection(top_features_list[j]))
            stability_score += intersection / 3

    # 归一化稳定性分数
    max_comparisons = n_runs * (n_runs - 1) / 2
    stability_score /= max_comparisons

    # 验证稳定性分数是否足够高（至少0.7）
    assert stability_score >= 0.7, f"特征重要性稳定性分数应该至少为0.7，实际为{stability_score:.2f}"


def test_importance_vs_prediction_power():
    """测试特征重要性与实际预测能力的关系"""
    # 创建数据集
    X, y = create_synthetic_dataset(n_samples=1000, n_features=10, n_informative=3)

    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    # 训练完整模型
    full_model = lgb.LGBMRegressor(**FeatureSelector.DEFAULT_LGBM_PARAMS)
    full_model.fit(X_train, y_train)

    # 获取特征重要性
    importance = pd.Series(full_model.feature_importances_, index=X.columns)
    importance = importance / importance.sum()

    # 获取前3个最重要的特征
    top_features = importance.sort_values(ascending=False).index.tolist()[:3]

    # 只使用前3个特征训练模型
    top_model = lgb.LGBMRegressor(**FeatureSelector.DEFAULT_LGBM_PARAMS)
    top_model.fit(X_train[top_features], y_train)

    # 使用随机3个特征训练模型
    np.random.seed(42)
    random_features = np.random.choice(X.columns, 3, replace=False).tolist()
    random_model = lgb.LGBMRegressor(**FeatureSelector.DEFAULT_LGBM_PARAMS)
    random_model.fit(X_train[random_features], y_train)

    # 评估三个模型
    full_pred = full_model.predict(X_test)
    top_pred = top_model.predict(X_test[top_features])
    random_pred = random_model.predict(X_test[random_features])

    full_mse = mean_squared_error(y_test, full_pred)
    top_mse = mean_squared_error(y_test, top_pred)
    random_mse = mean_squared_error(y_test, random_pred)

    # 验证使用重要特征的模型性能是否接近完整模型
    assert top_mse < random_mse, "使用重要特征的模型应该优于使用随机特征的模型"
    assert top_mse / full_mse < 1.5, "使用重要特征的模型性能应该接近完整模型"


def test_importance_with_different_dataset_sizes():
    """测试不同数据集大小下的特征重要性"""
    # 创建大数据集
    X_large, y_large = create_synthetic_dataset(n_samples=1000, n_features=10, n_informative=3)

    # 创建小数据集（使用大数据集的子集）
    X_small = X_large.iloc[:100]
    y_small = y_large[:100]

    # 在大数据集上训练模型
    large_model = lgb.LGBMRegressor(**FeatureSelector.DEFAULT_LGBM_PARAMS)
    large_model.fit(X_large, y_large)

    # 在小数据集上训练模型
    small_model = lgb.LGBMRegressor(**FeatureSelector.DEFAULT_LGBM_PARAMS)
    small_model.fit(X_small, y_small)

    # 获取特征重要性
    large_importance = pd.Series(large_model.feature_importances_, index=X_large.columns)
    large_importance = large_importance / large_importance.sum()

    small_importance = pd.Series(small_model.feature_importances_, index=X_small.columns)
    small_importance = small_importance / small_importance.sum()

    # 获取前3个最重要的特征
    large_top_features = set(large_importance.sort_values(ascending=False).index.tolist()[:3])
    small_top_features = set(small_importance.sort_values(ascending=False).index.tolist()[:3])

    # 计算重叠度
    overlap = len(large_top_features.intersection(small_top_features))

    # 验证重叠度是否足够高
    assert overlap >= 2, f"大小数据集的重要特征重叠度应该至少为2，实际为{overlap}"


def test_importance_with_outliers():
    """测试特征重要性对异常值的敏感性"""
    # 创建正常数据集
    X_normal, y_normal = create_synthetic_dataset(n_samples=1000, n_features=10, n_informative=3)

    # 创建带异常值的数据集（复制正常数据集并添加异常值）
    X_outlier = X_normal.copy()
    y_outlier = y_normal.copy()

    # 添加异常值（将1%的数据设置为极端值）
    np.random.seed(42)
    outlier_indices = np.random.choice(len(X_outlier), size=int(0.01 * len(X_outlier)), replace=False)
    for idx in outlier_indices:
        # 随机选择一个特征添加异常值
        feature_idx = np.random.randint(0, X_outlier.shape[1])
        X_outlier.iloc[idx, feature_idx] = 100.0  # 极端值

    # 在正常数据集上训练模型
    normal_model = lgb.LGBMRegressor(**FeatureSelector.DEFAULT_LGBM_PARAMS)
    normal_model.fit(X_normal, y_normal)

    # 在带异常值的数据集上训练模型
    outlier_model = lgb.LGBMRegressor(**FeatureSelector.DEFAULT_LGBM_PARAMS)
    outlier_model.fit(X_outlier, y_outlier)

    # 获取特征重要性
    normal_importance = pd.Series(normal_model.feature_importances_, index=X_normal.columns)
    normal_importance = normal_importance / normal_importance.sum()

    outlier_importance = pd.Series(outlier_model.feature_importances_, index=X_outlier.columns)
    outlier_importance = outlier_importance / outlier_importance.sum()

    # 获取前3个最重要的特征
    normal_top_features = set(normal_importance.sort_values(ascending=False).index.tolist()[:3])
    outlier_top_features = set(outlier_importance.sort_values(ascending=False).index.tolist()[:3])

    # 计算重叠度
    overlap = len(normal_top_features.intersection(outlier_top_features))

    # 验证重叠度是否足够高（LightGBM对异常值相对鲁棒）
    assert overlap >= 2, f"正常数据集和带异常值数据集的重要特征重叠度应该至少为2，实际为{overlap}"
