"""特征编码模块 - 处理条件特征编码
模块路径: src/models/gan/components/feature_encoder.py

继承自:
- src/models/base/base_module.py: BaseModule

协作模块:
- src/models/gan/components/multi_scale_feature_extractor.py
- src/models/gan/attention_components.py

优化特性:
- 先行信号增强机制：时序因果注意力、滞后相关性权重
- 多尺度时序建模：分层时序编码、自适应时间窗口
"""
import torch
from torch import Tensor, nn
import torch.nn.functional as F
from typing import cast # 导入 cast

from src.models.base.base_module import BaseModule
from src.utils.config_manager import ConfigManager  # 导入 ConfigManager
from src.utils.config.model import GANModelConfig, AttentionConfig, FeatureExtractorConfig # 导入配置类

# FeatureAttention import removed as it's unused
# MultiHeadAttention已被TemporalMultiHeadWrapper替代，提供更优的PyTorch原生实现
from ..feature_extractor import MultiScaleFeatureExtractor
from .multi_scale_temporal import MultiScaleTemporalModule
from .temporal import TemporalMultiHeadWrapper  # 优化的时序多头注意力实现


class CausalAttention(BaseModule):
    """时序因果注意力机制 - 确保只关注历史信息，避免未来泄露"""

    def __init__(self, embed_dim: int, num_heads: int, dropout: float = 0.1):
        super().__init__("CausalAttention")
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads

        assert embed_dim % num_heads == 0, f"embed_dim ({embed_dim}) 必须能被 num_heads ({num_heads}) 整除"

        # 查询、键、值投影
        self.q_proj = nn.Linear(embed_dim, embed_dim)
        self.k_proj = nn.Linear(embed_dim, embed_dim)
        self.v_proj = nn.Linear(embed_dim, embed_dim)
        self.out_proj = nn.Linear(embed_dim, embed_dim)

        self.dropout = nn.Dropout(dropout)
        self.scale = 1.0 / (self.head_dim ** 0.5)

    def forward(self, x: torch.Tensor) -> tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            x: [batch_size, seq_len, embed_dim]
        Returns:
            output: [batch_size, seq_len, embed_dim]
            attention_weights: [batch_size, num_heads, seq_len, seq_len]
        """
        batch_size, seq_len, _ = x.shape

        # 投影到查询、键、值
        q = self.q_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.k_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.v_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)

        # 计算注意力分数
        scores = torch.matmul(q, k.transpose(-2, -1)) * self.scale

        # 创建因果掩码 - 只允许关注当前和过去的时间步
        causal_mask = torch.tril(torch.ones(seq_len, seq_len, device=x.device, dtype=torch.bool))
        scores = scores.masked_fill(~causal_mask.unsqueeze(0).unsqueeze(0), float('-inf'))

        # 应用softmax
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)

        # 应用注意力权重
        out = torch.matmul(attention_weights, v)
        out = out.transpose(1, 2).contiguous().view(batch_size, seq_len, self.embed_dim)

        return self.out_proj(out), attention_weights


class LeadingSignalAmplifier(BaseModule):
    """先行信号放大器 - 增强预测性强的特征信号"""

    def __init__(self, feature_dim: int, amplification_factor: float = 2.0):
        super().__init__("LeadingSignalAmplifier")
        self.feature_dim = feature_dim
        self.amplification_factor = amplification_factor

        # 信号重要性评估网络
        self.importance_net = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.ReLU(),
            nn.Linear(feature_dim // 2, feature_dim),
            nn.Sigmoid()  # 输出0-1的重要性权重
        )

        # 信号放大网络
        self.amplifier_net = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.Tanh()  # 保持信号在合理范围内
        )

    def forward(self, features: torch.Tensor) -> torch.Tensor:
        """
        Args:
            features: [batch_size, seq_len, feature_dim]
        Returns:
            amplified_features: [batch_size, seq_len, feature_dim]
        """
        # 计算特征重要性权重
        importance_weights = self.importance_net(features)

        # 放大重要特征
        amplified_signals = self.amplifier_net(features)

        # 根据重要性权重混合原始特征和放大特征
        output = features + importance_weights * amplified_signals * self.amplification_factor

        return output


class FeatureEncoder(BaseModule):
    def __init__(self, config: ConfigManager, input_dim: int, feature_extractor_config: FeatureExtractorConfig):
        super().__init__("FeatureEncoder")

        # 验证输入维度
        if input_dim <= 0:
             raise ValueError(f"input_dim ({input_dim}) 必须为正整数")

        # 从 feature_extractor_config 获取 MultiScaleFeatureExtractor 的 hidden_dim
        msfe_hidden_dim = feature_extractor_config.msfe_hidden_dim
        if msfe_hidden_dim <= 0:
            raise ValueError(f"feature_extractor_config.msfe_hidden_dim ({msfe_hidden_dim}) 必须为正整数")

        self.logger.info(f"初始化特征编码器: input_dim={input_dim}, msfe_hidden_dim={msfe_hidden_dim}")

        # 从配置读取注意力参数，强制要求存在
        try:
            # 注意力参数在 model.attention 下
            # 使用 cast 来帮助 Pylance 理解类型
            gan_model_config = cast(GANModelConfig, config.model)
            attention_config = cast(AttentionConfig, gan_model_config.attention)

            num_heads = attention_config.multi_head_num_heads # 使用新的字段名
            dropout_attention = attention_config.multi_head_dropout # 使用新的字段名

            self.logger.info(f"从配置加载 MultiHeadAttention 参数: num_heads={num_heads}, dropout={dropout_attention}")
        except AttributeError as e:
            # 提供更明确的错误信息
            self.logger.error(f"访问配置时发生 AttributeError: {e}", exc_info=True)
            raise AttributeError(f"配置缺失必要的模型注意力参数 (例如 model.attention.multi_head_num_heads 或 model.attention.multi_head_dropout): {e}") from e
        except ValueError as e: # 通常由 AttentionConfig 的 __post_init__ 抛出
             self.logger.error(f"配置中的模型注意力参数无效: {e}", exc_info=True)
             raise ValueError(f"配置中的模型注意力参数无效: {e}") from e


        self.encoder = MultiScaleFeatureExtractor(
            input_dim=input_dim,
            hidden_dim=msfe_hidden_dim, # 使用从配置中获取的 hidden_dim
            num_scales=feature_extractor_config.msfe_num_scales,
            kernel_sizes=feature_extractor_config.msfe_kernel_sizes,
            dropout=feature_extractor_config.msfe_dropout
        )

        # === 先行信号增强机制组件 ===
        # 1. 时序因果注意力 - 确保只关注历史信息
        self.causal_attention = CausalAttention(
            embed_dim=msfe_hidden_dim,
            num_heads=num_heads,
            dropout=dropout_attention
        )

        # 2. 先行信号放大器 - 增强预测性强的特征
        self.signal_amplifier = LeadingSignalAmplifier(
            feature_dim=msfe_hidden_dim,
            amplification_factor=1.5  # 适度放大，避免过度增强
        )

        # 3. 多尺度时序建模模块 - 分层时序编码和自适应时间窗口
        self.multi_scale_temporal = MultiScaleTemporalModule(
            feature_dim=msfe_hidden_dim,
            hidden_dim=msfe_hidden_dim,
            max_window_size=48  # 最大时间窗口大小
        )

        # 4. 优化的时序多头注意力用于特征加权选择 (替代原有MultiHeadAttention)
        self.feature_weighting_attention = TemporalMultiHeadWrapper(
            hidden_dim=msfe_hidden_dim, # 使用 msfe_hidden_dim
            num_heads=num_heads, # 使用配置值
            dropout=dropout_attention # 使用配置值
        )

        # 应用权重初始化
        self.apply(self._init_weights)
        self.logger.info(f"特征编码器初始化完成，参数数量: {self.count_parameters():,}")

    def forward(self, features: Tensor) -> Tensor:
        """处理条件特征

        Args:
            features: [batch, seq_len, feature_dim]

        Returns:
            编码后的特征 [batch, seq_len, output_dim]
        """
        import time
        from datetime import datetime

        start_time = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 特征编码器开始处理 - 输入形状: {features.shape}")

        # 验证输入张量
        validation_start = time.time()
        self.validate_tensor(features, "输入特征", expected_dims=3)
        self.logger.debug(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 输入验证完成 - 耗时: {time.time() - validation_start:.2f}秒")

        # 基础特征编码
        encoding_start = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始基础特征编码")
        encoded_features = self.encoder(features)
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 基础特征编码完成 - 形状: {encoded_features.shape}, 耗时: {time.time() - encoding_start:.2f}秒")

        # 检查数值稳定性
        stability_start = time.time()
        self._check_numerical_stability(encoded_features, "编码后特征")
        self.logger.debug(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 数值稳定性检查完成 - 耗时: {time.time() - stability_start:.2f}秒")

        # === 先行信号增强处理流程 ===
        # 1. 应用时序因果注意力 - 确保只关注历史信息
        causal_start = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始时序因果注意力处理")
        causal_features, causal_weights = self.causal_attention(encoded_features)
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 时序因果注意力完成 - 形状: {causal_features.shape}, 耗时: {time.time() - causal_start:.2f}秒")
        self._check_numerical_stability(causal_features, "因果注意力后特征")

        # 2. 应用先行信号放大器 - 增强预测性强的特征
        amplify_start = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始先行信号放大")
        amplified_features = self.signal_amplifier(causal_features)
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 先行信号放大完成 - 形状: {amplified_features.shape}, 耗时: {time.time() - amplify_start:.2f}秒")
        self._check_numerical_stability(amplified_features, "信号放大后特征")

        # 3. 应用多尺度时序建模 - 分层时序编码和自适应时间窗口
        temporal_start = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始多尺度时序建模")
        temporal_features = self.multi_scale_temporal(amplified_features)
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 多尺度时序建模完成 - 形状: {temporal_features.shape}, 耗时: {time.time() - temporal_start:.2f}秒")
        self._check_numerical_stability(temporal_features, "时序建模后特征")

        # 4. 应用优化的时序多头注意力进行特征加权选择
        attention_start = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始特征加权注意力")
        # Apply TemporalMultiHeadWrapper to the temporal features (更优的PyTorch原生实现)
        weighted_features, attention_weights = self.feature_weighting_attention(
            x=temporal_features,
            mask=None  # 不使用掩码，允许全局注意力
        )
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 多头注意力应用完成 - 形状: {weighted_features.shape}, 耗时: {time.time() - attention_start:.2f}秒")

        # 检查数值稳定性
        stability_start = time.time()
        self._check_numerical_stability(weighted_features, "加权后特征")
        self.logger.debug(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 数值稳定性检查完成 - 耗时: {time.time() - stability_start:.2f}秒")

        # 检查内存使用情况
        if torch.cuda.is_available():
            mem_allocated = torch.cuda.memory_allocated() / (1024 ** 2)
            mem_reserved = torch.cuda.memory_reserved() / (1024 ** 2)
            self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] CUDA内存使用情况 - 已分配: {mem_allocated:.2f}MB, 已保留: {mem_reserved:.2f}MB")

        end_time = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 特征编码器输出: shape={weighted_features.shape}, 总耗时: {end_time - start_time:.2f}秒")

        return weighted_features # Return features weighted by MultiHeadAttention
