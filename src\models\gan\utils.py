"""GAN工具函数模块 - 提供张量验证和数值稳定性检查工具

本模块提供了一组用于GAN训练过程中的工具函数，主要包括：
1. 数值稳定性检查和修复
2. 设备一致性验证
3. 张量形状兼容性检查
4. 输入张量有效性验证

主要功能：
- check_numerical_stability: 检查并可选修复张量中的NaN/Inf值
- check_device_consistency: 验证多个张量是否在同一设备上
- check_shape_compatibility: 检查多个张量的形状兼容性
- validate_tensor: 验证张量的基本有效性

使用示例:
```python
import torch
from src.models.gan.utils import check_numerical_stability, validate_tensor

# 验证并修复包含NaN的张量
tensor = torch.tensor([1.0, float('nan'), 3.0])
fixed_tensor = check_numerical_stability(tensor, "input", fix_tensor=True)

# 验证张量有效性
valid = validate_tensor(tensor, "input", expected_dims=2)
```

注意事项:
1. check_numerical_stability 在fix_tensor=True时会返回新的张量
2. 所有检查函数在发现问题时都会抛出相应的异常
3. 验证函数支持自定义错误消息和预期维度
"""


import torch

# 指定要导出的符号
__all__ = [
    'check_device_consistency',
    'check_numerical_stability',
    'check_shape_compatibility',
    'validate_tensor',
]

def check_numerical_stability(
    tensor: torch.Tensor,
    name: str,
    fix_tensor: bool = False
) -> torch.Tensor | None:
    """检查张量的数值稳定性，可选择修复NaN/Inf值

    Args:
        tensor: 要检查的张量
        name: 张量名称(用于日志)
        fix_tensor: 是否修复问题值

    Returns:
        Optional[torch.Tensor]: 如果fix_tensor为True则返回修复后的张量，否则返回None

    Raises:
        ValueError: 当张量全为NaN/Inf且无法修复时
    """
    # 确保使用float32类型进行检查，避免混合精度问题
    tensor_float32 = tensor.to(torch.float32)

    has_nan = torch.isnan(tensor_float32).any()
    has_inf = torch.isinf(tensor_float32).any()

    if not (has_nan or has_inf):
        return tensor if fix_tensor else None

    # 计算有效值的统计信息
    valid_mask = ~(torch.isnan(tensor_float32) | torch.isinf(tensor_float32))
    if not valid_mask.any():
        if fix_tensor:
            return torch.zeros_like(tensor)
        return None

    valid_tensor = tensor_float32[valid_mask]
    replacement_value = valid_tensor.mean().item()

    if fix_tensor:
        fixed_tensor = tensor.clone()
        if has_nan:
            fixed_tensor[torch.isnan(fixed_tensor)] = replacement_value
        if has_inf:
            fixed_tensor[torch.isinf(fixed_tensor)] = replacement_value
        return fixed_tensor

    return None

def check_device_consistency(*tensors: torch.Tensor) -> None:
    """检查多个张量是否在同一设备上

    Args:
        *tensors: 要检查的张量列表

    Raises:
        ValueError: 当张量不在同一设备上时
    """
    if not tensors:
        return

    reference_device = tensors[0].device
    for i, tensor in enumerate(tensors[1:], 1):
        if tensor.device != reference_device:
            raise ValueError(
                f"设备不一致: tensor[0]在{reference_device}，"
                f"但tensor[{i}]在{tensor.device}"
            )

def check_shape_compatibility(
    *tensors: torch.Tensor,
    expected_shapes: list[tuple[int, ...]] | None = None
) -> None:
    """检查多个张量的形状兼容性

    Args:
        *tensors: 要检查的张量列表
        expected_shapes: 期望的形状列表

    Raises:
        ValueError: 当张量形状不兼容或与期望形状不匹配时
    """
    if not tensors:
        return

    if expected_shapes:
        if len(tensors) != len(expected_shapes):
            raise ValueError(
                f"张量数量({len(tensors)})与期望形状数量({len(expected_shapes)})不匹配"
            )
        for i, (tensor, expected) in enumerate(zip(tensors, expected_shapes, strict=False)):
            if tensor.shape != expected:
                raise ValueError(
                    f"张量[{i}]形状{tensor.shape}与期望形状{expected}不匹配"
                )
    else:
        reference_shape = tensors[0].shape
        for i, tensor in enumerate(tensors[1:], 1):
            if tensor.shape != reference_shape:
                raise ValueError(
                    f"形状不一致: tensor[0]为{reference_shape}，"
                    f"但tensor[{i}]为{tensor.shape}"
                )

def validate_tensor(
    tensor: torch.Tensor,
    name: str,
    expected_dims: int | None = None
) -> bool:
    """验证张量的基本有效性

    Args:
        tensor: 要验证的张量
        name: 张量名称(用于错误消息)
        expected_dims: 期望的维度数(可选)

    Returns:
        bool: 验证是否通过

    Raises:
        ValueError: 当验证失败时
    """
    # 检查类型
    if not isinstance(tensor, torch.Tensor):
        raise ValueError(f"{name}必须是torch.Tensor类型，而不是{type(tensor)}")

    # 检查维度
    if expected_dims is not None and tensor.dim() != expected_dims:
        raise ValueError(f"{name}必须是{expected_dims}维张量，而不是{tensor.dim()}维")

    # 检查是否包含NaN/Inf
    if torch.isnan(tensor).any():
        raise ValueError(f"{name}包含NaN值")
    if torch.isinf(tensor).any():
        raise ValueError(f"{name}包含Inf值")

    return True
