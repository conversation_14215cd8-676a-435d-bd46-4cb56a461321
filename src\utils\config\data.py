"""数据配置模块 - 管理数据处理和特征工程相关配置"""

from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Any

from src.utils.config.base import BaseConfig
from src.utils.config.paths import PathsConfig

# 修正：确保 get_logger 被导入在文件顶部
from src.utils.logger import get_logger


@dataclass
class DataConfig(BaseConfig):
    """数据配置类

    主要职责:
    1. 管理数据路径和目标列配置
    2. 数据切分和预处理参数
    3. 特征工程和数据增强设置
    4. 窗口和序列相关参数
    """
    # 移除默认值，强制从配置文件加载
    # 移除所有 Optional 和默认值，所有字段现在都是必需的，除非明确标记为 Optional 且不由 __post_init__ 检查
    paths: PathsConfig
    data_path: str
    model_path: str # model_path 可能在某些流程中不需要，但 DataConfig 本身需要它
    target: str
    prediction_horizon: int
    preprocessing: dict[str, Any]
    columns: dict[str, Any]
    window_size: int
    stride: int
    # feature_dim moved down
    train_ratio: float
    val_ratio: float
    test_ratio: float
    batch_size: int
    num_workers: int
    pin_memory: bool
    shuffle: bool
    drop_last: bool
    sequence_strategy: dict[str, Any]
    feature_selection: dict[str, Any]
    load_period: str # 用于控制时间序列截取范围，格式为 'start_date/end_date'
    # Fields with defaults moved to the end:
    feature_dim: int | None = None # 改为 Optional，允许动态设置

    def __post_init__(self):
        """初始化数据配置，检查必需字段并在缺失时抛出异常"""
        logger = get_logger(__name__)

        # 检查必需字段
        missing_fields = []

        # 检查所有必需字段是否存在且类型正确
        # (移除 is None 检查，因为字段不再是 Optional)
        # 从必需字段检查中移除 feature_dim
        required_attributes = [
            'paths', 'data_path', 'model_path', 'target', 'prediction_horizon',
            'preprocessing', 'columns', 'window_size', 'stride', # 'feature_dim' removed
            'train_ratio', 'val_ratio', 'test_ratio', 'batch_size', 'num_workers',
            'pin_memory', 'shuffle', 'drop_last', 'sequence_strategy',
            'feature_selection', 'load_period'
            # 'optimization_start_date' is Optional
        ]
        for attr_name in required_attributes:
            if not hasattr(self, attr_name) or getattr(self, attr_name) is None: # 检查 None 以防万一
                 missing_fields.append(attr_name)

        if missing_fields:
            error_msg = f"DataConfig 初始化时缺少必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # --- 验证 paths 类型 ---
        if not isinstance(self.paths, PathsConfig):
             error_msg = f"'paths' 字段必须是 PathsConfig 类型，但得到 {type(self.paths)}"
             logger.error(error_msg)
             raise TypeError(error_msg)
        # --- 移除默认 PathsConfig 创建 ---

        # 1. 验证比例配置
        # 字段不再是 Optional，移除 assert
        train_ratio_float = float(self.train_ratio)
        val_ratio_float = float(self.val_ratio)
        test_ratio_float = float(self.test_ratio)

        total_ratio = train_ratio_float + val_ratio_float + test_ratio_float
        if not 0.99 <= total_ratio <= 1.01:  # 允许浮点误差
            raise ValueError(f"数据集比例之和必须接近 1.0，当前值: {total_ratio}")

        # 2. 验证 sequence_strategy 结构和一致性
        if not isinstance(self.sequence_strategy, dict):
             error_msg = f"'sequence_strategy' 必须是字典类型，但得到 {type(self.sequence_strategy)}"
             logger.error(error_msg)
             raise TypeError(error_msg)
        # 移除默认字典创建

        # 检查 sequence_strategy 是否包含 window_size 和 stride，并与顶层配置一致
        if 'window_size' not in self.sequence_strategy or self.sequence_strategy['window_size'] != self.window_size:
             raise ValueError(f"sequence_strategy 中的 'window_size' ({self.sequence_strategy.get('window_size')}) "
                              f"必须存在且等于顶层的 window_size ({self.window_size})")
        if 'stride' not in self.sequence_strategy or self.sequence_strategy['stride'] != self.stride:
             raise ValueError(f"sequence_strategy 中的 'stride' ({self.sequence_strategy.get('stride')}) "
                              f"必须存在且等于顶层的 stride ({self.stride})")

        # 3. 验证预处理配置类型
        if not isinstance(self.preprocessing, dict):
            error_msg = f"'preprocessing' 必须是字典类型，但得到 {type(self.preprocessing)}"
            logger.error(error_msg)
            raise TypeError(error_msg)
        if not self.preprocessing: # 检查是否为空字典
            # logger.warning("预处理配置 'preprocessing' 为空字典") # 改为警告，允许空配置
            error_msg = "预处理配置 'preprocessing' 不能为空字典" # 改为错误
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 4. 验证列配置类型
        if not isinstance(self.columns, dict):
            error_msg = f"'columns' 必须是字典类型，但得到 {type(self.columns)}"
            logger.error(error_msg)
            raise TypeError(error_msg)
        if not self.columns: # 检查是否为空字典
            error_msg = "列配置 'columns' 不能为空字典"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 5. 记录配置信息 (保持不变)
        logger.debug(
            f"数据配置已加载:\n"
            f"- 特征维度: {self.feature_dim}\n"
            f"- 窗口大小: {self.window_size}\n"
            f"- 步长: {self.stride}\n"
            f"- 目标列: {self.target}\n"
            f"- 数据路径: {self.data_path}"
        )

    def update_feature_dim_from_model(self, model_path: str | Path | None) -> None:
        """从模型文件中更新特征维度 (根据用户反馈，此功能已禁用)

        Args:
            model_path: 模型文件路径，可以为None
        """
        logger = get_logger(__name__)
        logger.info("根据用户反馈，已禁用从模型文件更新特征维度的功能。特征维度将保持不变或由其他方式设置。")
        if model_path:
            logger.debug(f"原计划检查的模型路径: {model_path}")

@dataclass
class FeatureValidationConfig(BaseConfig):
    """特征验证配置"""
    # 移除 Optional 和默认值
    enable: bool
    check_nan: bool
    check_range: bool
    min_importance: float

    def __post_init__(self):
        """初始化特征验证配置，检查必需字段并在缺失时抛出异常"""
        logger = get_logger(__name__)

        # 检查必需字段
        missing_fields = []

        # 检查必需字段是否存在 (移除 is None)
        if not hasattr(self, 'enable'):
            missing_fields.append('enable')
        if not hasattr(self, 'check_nan'):
            missing_fields.append('check_nan')
        if not hasattr(self, 'check_range'):
            missing_fields.append('check_range')
        if not hasattr(self, 'min_importance'):
            missing_fields.append('min_importance')

        # 如果有缺失字段，抛出异常
        if missing_fields:
            error_msg = f"特征验证配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

# --- 新增：为 FeatureEngineeringConfig 定义嵌套结构 (移除 BaseConfig 继承) ---
@dataclass
class BaseFeaturesConfig:
    # 移除默认值
    enable: bool
    keep_original: bool

    def __post_init__(self):
        """初始化基础特征配置，检查必需字段"""
        logger = get_logger(__name__)
        missing_fields = []
        if not hasattr(self, 'enable'):
            missing_fields.append('enable')
        if not hasattr(self, 'keep_original'):
            missing_fields.append('keep_original')

        if missing_fields:
            error_msg = f"基础特征配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

@dataclass
class DiffFeaturesConfig:
    enable: bool
    orders: list[int]

    def __post_init__(self):
        """初始化差分特征配置，检查必需字段并在缺失时抛出异常"""
        logger = get_logger(__name__)

        # 检查必需字段
        missing_fields = []
        if not hasattr(self, 'enable'):
             missing_fields.append('enable')
        if not hasattr(self, 'orders') or not self.orders: # 检查 orders 是否存在且不为空
             missing_fields.append('orders')

        if missing_fields:
             error_msg = f"差分特征配置缺失必需字段: {', '.join(missing_fields)}"
             logger.error(error_msg)
             raise ValueError(error_msg)

        # 验证 orders 中的值
        for order in self.orders:
            if not isinstance(order, int) or order <= 0:
                error_msg = f"无效的差分阶数: {order}，必须为正整数"
                logger.error(error_msg)
                raise ValueError(error_msg)

@dataclass
class LagFeaturesConfig:
    # 移除默认值
    enable: bool
    max_lag: int
    step: int

    def __post_init__(self):
        """初始化滞后特征配置，检查必需字段"""
        logger = get_logger(__name__)
        missing_fields = []
        if not hasattr(self, 'enable'):
            missing_fields.append('enable')
        if not hasattr(self, 'max_lag'):
            missing_fields.append('max_lag')
        if not hasattr(self, 'step'):
            missing_fields.append('step')

        if missing_fields:
            error_msg = f"滞后特征配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 添加值验证
        if self.max_lag <= 0:
             raise ValueError(f"max_lag 必须为正整数, 得到 {self.max_lag}")
        if self.step <= 0:
             raise ValueError(f"step 必须为正整数, 得到 {self.step}")

@dataclass
class WindowFeaturesConfig:
    # 移除默认值和 default_factory
    enable: bool
    window_sizes: list[int]
    stats: list[str]

    def __post_init__(self):
        """初始化窗口特征配置，检查必需字段"""
        logger = get_logger(__name__)
        missing_fields = []
        if not hasattr(self, 'enable'):
            missing_fields.append('enable')
        if not hasattr(self, 'window_sizes') or not self.window_sizes: # 检查是否为空
            missing_fields.append('window_sizes')
        if not hasattr(self, 'stats') or not self.stats: # 检查是否为空
            missing_fields.append('stats')

        if missing_fields:
            error_msg = f"窗口特征配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 添加值验证
        for size in self.window_sizes:
            if not isinstance(size, int) or size <= 0:
                raise ValueError(f"window_sizes 必须包含正整数, 得到 {size}")
        valid_stats = ["mean", "std", "min", "max", "median", "sum", "skew", "kurt"] # 扩展允许的统计量
        for stat in self.stats:
            if stat not in valid_stats:
                raise ValueError(f"stats 包含无效值: {stat}. 允许的值: {valid_stats}")

@dataclass
class VolatilityFeaturesConfig:
    # 移除默认值
    enable: bool
    model_type: str
    p: int
    q: int
    dist: str
    scale_threshold: int

    def __post_init__(self):
        """初始化波动率特征配置，检查必需字段"""
        logger = get_logger(__name__)
        missing_fields = []
        if not hasattr(self, 'enable'):
            missing_fields.append('enable')
        if not hasattr(self, 'model_type'):
            missing_fields.append('model_type')
        if not hasattr(self, 'p'):
            missing_fields.append('p')
        if not hasattr(self, 'q'):
            missing_fields.append('q')
        if not hasattr(self, 'dist'):
            missing_fields.append('dist')
        if not hasattr(self, 'scale_threshold'):
            missing_fields.append('scale_threshold')

        if missing_fields:
            error_msg = f"波动率特征配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 添加值验证
        valid_models = ["GARCH", "EGARCH"] # 示例
        if self.model_type not in valid_models:
            raise ValueError(f"model_type 无效: {self.model_type}. 允许的值: {valid_models}")
        if self.p < 0:
            raise ValueError(f"p 不能为负数: {self.p}")
        if self.q < 0:
            raise ValueError(f"q 不能为负数: {self.q}")
        valid_dists = ["normal", "t"] # 示例
        if self.dist not in valid_dists:
            raise ValueError(f"dist 无效: {self.dist}. 允许的值: {valid_dists}")
        if self.scale_threshold <= 0:
            raise ValueError(f"scale_threshold 必须为正: {self.scale_threshold}")

@dataclass
class TimeSeriesFeaturesConfig:
    # 移除默认值和 default_factory，所有嵌套配置现在都是必需的
    diff_features: DiffFeaturesConfig
    enable: bool
    lag_features: LagFeaturesConfig
    window_features: WindowFeaturesConfig
    volatility_features: VolatilityFeaturesConfig

    def __post_init__(self):
        """初始化时间序列特征配置，检查必需字段并在缺失时抛出异常"""
        logger = get_logger(__name__)

        # 检查所有必需字段
        missing_fields = []
        if not hasattr(self, 'enable'):
            missing_fields.append('enable')
        if not hasattr(self, 'diff_features') or not isinstance(self.diff_features, DiffFeaturesConfig):
            missing_fields.append('diff_features (必须是 DiffFeaturesConfig 类型)')
        if not hasattr(self, 'lag_features') or not isinstance(self.lag_features, LagFeaturesConfig):
            missing_fields.append('lag_features (必须是 LagFeaturesConfig 类型)')
        if not hasattr(self, 'window_features') or not isinstance(self.window_features, WindowFeaturesConfig):
            missing_fields.append('window_features (必须是 WindowFeaturesConfig 类型)')
        if not hasattr(self, 'volatility_features') or not isinstance(self.volatility_features, VolatilityFeaturesConfig):
            missing_fields.append('volatility_features (必须是 VolatilityFeaturesConfig 类型)')

        if missing_fields:
            error_msg = f"时间序列特征配置缺失或类型错误: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

@dataclass
class PCAConfig:
    # 移除默认值
    enable: bool
    n_components: int | float | None = None  # 明确设置默认值为None，确保被识别为Optional

    def __post_init__(self):
        """初始化 PCA 配置，检查必需字段"""
        logger = get_logger(__name__)
        missing_fields = []
        if not hasattr(self, 'enable'):
            missing_fields.append('enable')
        # n_components 可以是 None，所以只检查 enable
        # if not hasattr(self, 'n_components'): missing_fields.append('n_components') # 不需要检查 None

        if missing_fields:
            error_msg = f"PCA 配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 添加值验证
        if self.enable and self.n_components is not None:
            if isinstance(self.n_components, int) and self.n_components <= 0:
                 raise ValueError(f"n_components 如果是整数，必须 > 0, 得到 {self.n_components}")
            if isinstance(self.n_components, float) and not (0 < self.n_components <= 1):
                 raise ValueError(f"n_components 如果是浮点数，必须在 (0, 1] 之间, 得到 {self.n_components}")

@dataclass
class StatisticalFeaturesConfig:
    # 移除默认值和 default_factory，嵌套配置变为必需
    enable: bool
    correlation: bool
    covariance: bool
    pca: PCAConfig
    rolling_stats: WindowFeaturesConfig # 复用 WindowFeaturesConfig

    def __post_init__(self):
        """初始化统计特征配置，检查必需字段"""
        logger = get_logger(__name__)
        missing_fields = []
        if not hasattr(self, 'enable'):
            missing_fields.append('enable')
        if not hasattr(self, 'correlation'):
            missing_fields.append('correlation')
        if not hasattr(self, 'covariance'):
            missing_fields.append('covariance')
        if not hasattr(self, 'pca') or not isinstance(self.pca, PCAConfig):
             missing_fields.append('pca (必须是 PCAConfig 类型)')
        if not hasattr(self, 'rolling_stats') or not isinstance(self.rolling_stats, WindowFeaturesConfig):
             missing_fields.append('rolling_stats (必须是 WindowFeaturesConfig 类型)')

        if missing_fields:
            error_msg = f"统计特征配置缺失或类型错误: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

@dataclass
class FrequencyFeaturesConfig:
    # 移除默认值
    enable: bool
    fft: bool
    spectral_density: bool

    def __post_init__(self):
        """初始化频率特征配置，检查必需字段"""
        logger = get_logger(__name__)
        missing_fields = []
        if not hasattr(self, 'enable'):
            missing_fields.append('enable')
        if not hasattr(self, 'fft'):
            missing_fields.append('fft')
        if not hasattr(self, 'spectral_density'):
            missing_fields.append('spectral_density')

        if missing_fields:
            error_msg = f"频率特征配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

@dataclass
class OutlierDetectionConfig:
    # 移除默认值
    enable: bool
    method: str
    threshold: float

    def __post_init__(self):
        """初始化离群点检测配置，检查必需字段"""
        logger = get_logger(__name__)
        missing_fields = []
        if not hasattr(self, 'enable'):
            missing_fields.append('enable')
        if not hasattr(self, 'method'):
            missing_fields.append('method')
        if not hasattr(self, 'threshold'):
            missing_fields.append('threshold')

        if missing_fields:
            error_msg = f"离群点检测配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 添加值验证
        valid_methods = ["iqr", "zscore", "isolation_forest"] # 示例
        if self.method not in valid_methods:
            raise ValueError(f"method 无效: {self.method}. 允许的值: {valid_methods}")
        if self.threshold <= 0:
            raise ValueError(f"threshold 必须为正数: {self.threshold}")

@dataclass
class QualityControlConfig:
    # 移除默认值和 default_factory，嵌套配置变为必需
    enable: bool
    drop_na: bool
    drop_duplicates: bool
    outlier_detection: OutlierDetectionConfig

    def __post_init__(self):
        """初始化质量控制配置，检查必需字段"""
        logger = get_logger(__name__)
        missing_fields = []
        if not hasattr(self, 'enable'):
            missing_fields.append('enable')
        if not hasattr(self, 'drop_na'):
            missing_fields.append('drop_na')
        if not hasattr(self, 'drop_duplicates'):
            missing_fields.append('drop_duplicates')
        if not hasattr(self, 'outlier_detection') or not isinstance(self.outlier_detection, OutlierDetectionConfig):
             missing_fields.append('outlier_detection (必须是 OutlierDetectionConfig 类型)')

        if missing_fields:
            error_msg = f"质量控制配置缺失或类型错误: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

# 定义候选特征选择配置
@dataclass
class CandidateSelectionConfig:
    enable: bool
    methods: list[str]
    combination_logic: str
    lag_corr: dict[str, Any]
    mutual_info: dict[str, Any]
    top_n_final_candidates: int

    def __post_init__(self):
        """初始化候选特征选择配置，检查必需字段并在缺失时抛出异常"""
        logger = get_logger(__name__)
        missing_fields = []

        # 检查类型和值约束
        if not isinstance(self.enable, bool):
            missing_fields.append("'enable' 必须是布尔值")
        if not isinstance(self.methods, list) or not self.methods:
            missing_fields.append("'methods' 必须是非空列表")
        if not isinstance(self.combination_logic, str) or self.combination_logic not in ['union', 'intersection']:
            missing_fields.append("'combination_logic' 必须是 'union' 或 'intersection'")
        if not isinstance(self.lag_corr, dict):
            missing_fields.append("'lag_corr' 必须是字典")
        if not isinstance(self.mutual_info, dict):
            missing_fields.append("'mutual_info' 必须是字典")
        if not isinstance(self.top_n_final_candidates, int) or self.top_n_final_candidates < 2:
            missing_fields.append("'top_n_final_candidates' 必须是 >= 2 的整数")

        if missing_fields:
            error_msg = f"候选特征选择配置校验失败: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

# 新增 InteractionFeaturesConfig dataclass 定义
@dataclass
class InteractionFeaturesConfig:
    enable: bool
    top_k: int
    candidate_selection: CandidateSelectionConfig | None = None

    def __post_init__(self):
        """初始化交互特征配置，检查必需字段并在缺失时抛出异常"""
        logger = get_logger(__name__)
        missing_fields = []

        # 检查类型和值约束
        if not isinstance(self.enable, bool):
            missing_fields.append("'enable' 必须是布尔值")
        if not isinstance(self.top_k, int) or self.top_k < 2:
             missing_fields.append("'top_k' 必须是 >= 2 的整数")

        # 检查候选特征选择配置（如果存在）
        if self.candidate_selection is not None and not isinstance(self.candidate_selection, CandidateSelectionConfig):
            missing_fields.append("'candidate_selection' 必须是 CandidateSelectionConfig 类型或 None")

        if missing_fields:
            error_msg = f"交互特征配置校验失败: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

@dataclass
class TimePreprocessingConfig:
    features_to_extract: list[str]
    # 如果还有其他 time_preprocessing 下的字段，也在这里定义

    def __post_init__(self):
        logger = get_logger(__name__)
        missing_fields = []
        if not hasattr(self, 'features_to_extract'):
            missing_fields.append('features_to_extract')
        elif not self.features_to_extract: # 检查是否为空列表
             missing_fields.append('features_to_extract (不能为空列表)')
        elif not all(isinstance(item, str) for item in self.features_to_extract):
             missing_fields.append('features_to_extract (必须是字符串列表)')


        if missing_fields:
            error_msg = f"TimePreprocessingConfig 校验失败: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

@dataclass
class LayerConfig:
    """定义特征工程层级结构中的单层配置"""
    level: int
    generators: list[str | dict[str, Any]]
    keep_input_features: bool # 移除默认值

    def __post_init__(self):
        """初始化层配置，检查必需字段"""
        logger = get_logger(__name__)
        missing_fields = []
        if not hasattr(self, 'level'):
            missing_fields.append('level')
        if not hasattr(self, 'generators') or not self.generators:  # 检查是否为空
            missing_fields.append('generators')
        if not hasattr(self, 'keep_input_features'):
            missing_fields.append('keep_input_features')

        if missing_fields:
            error_msg = f"层配置 (Level {getattr(self, 'level', '未知')}) 缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 添加值验证
        if not isinstance(self.level, int) or self.level < 0:
             raise ValueError(f"level 必须是非负整数, 得到 {self.level}")
        if not isinstance(self.generators, list):
             raise ValueError(f"generators 必须是列表, 得到 {type(self.generators)}")

@dataclass
class FeatureEngineeringConfig(BaseConfig):
    """特征工程配置 (使用嵌套 Dataclass 和层级结构)"""
    # --- 原始嵌套结构 (保留以兼容旧配置或特定场景) ---
    # 移除 Optional 和默认值/default_factory，大部分字段变为必需
    # 旧结构字段保持 Optional，但 __post_init__ 会检查它们是否与 layers 一起提供
    # --- 修正字段顺序：无默认值的字段在前 ---
    keep_original_in_final: bool # 移除默认值
    enable: bool # 移除默认值
    statistical_features: StatisticalFeaturesConfig
    frequency_features: FrequencyFeaturesConfig
    quality_control: QualityControlConfig
    columns: dict[str, Any] # 移除 default_factory
    time_preprocessing: TimePreprocessingConfig # 设为必需字段

    # --- 有默认值 (Optional) 的字段在后 ---
    # 旧结构字段保持 Optional，但 __post_init__ 会检查它们是否与 layers 一起提供
    base_features: BaseFeaturesConfig | None = None
    time_series_features: TimeSeriesFeaturesConfig | None = None
    # 层级结构配置 (layers 变为必需，如果 enable=True)
    layers: list[LayerConfig] | None = None # 保持 Optional，由 __post_init__ 检查
    interaction_features: InteractionFeaturesConfig | None = None # 保持 Optional


    def __post_init__(self):
        """初始化特征工程配置，检查必需字段并在缺失时抛出异常"""
        logger = get_logger(__name__)

        # 检查必需字段
        missing_fields = []

        # 检查必需字段 (移除 is None/default_factory 后)
        if not hasattr(self, 'enable'):
            missing_fields.append('enable')
        if not hasattr(self, 'keep_original_in_final'):
            missing_fields.append('keep_original_in_final')
        if not hasattr(self, 'statistical_features') or not isinstance(self.statistical_features, StatisticalFeaturesConfig):
             missing_fields.append('statistical_features (必须是 StatisticalFeaturesConfig 类型)')
        if not hasattr(self, 'frequency_features') or not isinstance(self.frequency_features, FrequencyFeaturesConfig):
             missing_fields.append('frequency_features (必须是 FrequencyFeaturesConfig 类型)')
        if not hasattr(self, 'quality_control') or not isinstance(self.quality_control, QualityControlConfig):
             missing_fields.append('quality_control (必须是 QualityControlConfig 类型)')
        if not hasattr(self, 'columns') or not isinstance(self.columns, dict) or not self.columns:
             missing_fields.append('columns (必须是非空字典)')
        elif 'time_features' not in self.columns or 'numeric' not in self.columns or 'categorical' not in self.columns:
             missing_fields.append('columns 必须包含 time_features, numeric, 和 categorical 键')

        # 检查 time_preprocessing 字段 (因为它是必需的)
        if not hasattr(self, 'time_preprocessing') or not isinstance(self.time_preprocessing, TimePreprocessingConfig):
            missing_fields.append('time_preprocessing (必须是 TimePreprocessingConfig 类型且存在)')


        # 条件检查：如果 enable=True，则 layers 或 (base_features 和 time_series_features) 必须存在
        if self.enable:
            layers_present = hasattr(self, 'layers') and self.layers is not None
            old_structure_present = (hasattr(self, 'base_features') and self.base_features is not None and
                                     hasattr(self, 'time_series_features') and self.time_series_features is not None)

            if not layers_present and not old_structure_present:
                 missing_fields.append("当 enable=True 时，必须提供 'layers' 或 ('base_features' 和 'time_series_features')")
            elif layers_present:
                 # 验证 layers 结构
                 if not isinstance(self.layers, list):
                     missing_fields.append("'layers' 必须是列表")
                 else:
                     for i, layer in enumerate(self.layers):
                         if not isinstance(layer, LayerConfig):
                             # 尝试从字典转换，如果失败则报错
                             try:
                                 # 假设 LayerConfig 可以从字典初始化
                                 # 注意：这依赖于 LayerConfig 的实现，如果它不能直接从字典创建，需要调整
                                 # 或者在加载阶段 (loader.py) 进行转换
                                 self.layers[i] = LayerConfig(**layer) # 尝试转换
                             except (TypeError, ValueError) as e:
                                 missing_fields.append(f"'layers' 元素 {i} 无法转换为 LayerConfig: {e}")
                         # LayerConfig 的 __post_init__ 会进行内部检查

            elif old_structure_present:
                 # 验证旧结构类型
                 if not isinstance(self.base_features, BaseFeaturesConfig):
                     missing_fields.append("'base_features' 必须是 BaseFeaturesConfig 类型")
                 if not isinstance(self.time_series_features, TimeSeriesFeaturesConfig):
                     missing_fields.append("'time_series_features' 必须是 TimeSeriesFeaturesConfig 类型")

        # 检查 interaction_features 类型 (如果存在)
        if hasattr(self, 'interaction_features') and self.interaction_features is not None and \
           not isinstance(self.interaction_features, InteractionFeaturesConfig):
             missing_fields.append("'interaction_features' 必须是 InteractionFeaturesConfig 类型或 None")

        if missing_fields:
            error_msg = f"特征工程配置校验失败: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

    # to_dict 方法可以保留，但现在可以使用 dataclasses.asdict
    # def to_dict(self) -> Dict[str, Any]:
    #     from dataclasses import asdict
    #     return asdict(self)

@dataclass
class LaggedCorrConfig(BaseConfig):
    min_abs_corr: float
    max_lag: int

    def __post_init__(self):
        if not (isinstance(self.min_abs_corr, float) and 0.0 <= self.min_abs_corr <= 1.0):
            raise ValueError(f"lagged_corr.min_abs_corr ({self.min_abs_corr}) must be a float between 0.0 and 1.0")
        if not (isinstance(self.max_lag, int) and self.max_lag > 0):
            raise ValueError(f"lagged_corr.max_lag ({self.max_lag}) must be a positive integer")

@dataclass
class NoiseDetectionConfig(BaseConfig):
    low_variance_threshold: float
    high_correlation_threshold: float

    def __post_init__(self):
        if not (isinstance(self.low_variance_threshold, float) and self.low_variance_threshold >= 0):
            raise ValueError(f"noise_detection.low_variance_threshold ({self.low_variance_threshold}) must be a non-negative float")
        if not (isinstance(self.high_correlation_threshold, float) and 0.0 <= self.high_correlation_threshold <= 1.0):
            raise ValueError(f"noise_detection.high_correlation_threshold ({self.high_correlation_threshold}) must be a float between 0.0 and 1.0")

@dataclass
class FeatureSelectionConfig(BaseConfig):
    enable: bool = False
    method: str | None = None # Example: 'filter', 'wrapper', 'embedded'
    top_k: int | None = None # General top_k if applicable across methods

    lagged_corr: LaggedCorrConfig | None = None
    noise_detection: NoiseDetectionConfig | None = None
    importance_threshold: float | None = None # Percentile, e.g., 0.05 for bottom 5%

    # Placeholder for other method-specific configs if needed in the future
    # correlation_params: Optional[Dict[str, Any]] = field(default_factory=dict)
    # importance_params: Optional[Dict[str, Any]] = field(default_factory=dict)

    def __post_init__(self):
        logger = get_logger(__name__)
        if self.enable:
            if self.method is None: # A general method might be useful
                # logger.warning("Feature selection is enabled, but 'method' is not specified.") # Replaced warning with error
                raise ValueError("Feature selection is enabled, but 'method' is not specified in the configuration.")

            # Validate nested configs if they exist
            if self.lagged_corr is not None and not isinstance(self.lagged_corr, LaggedCorrConfig):
                raise TypeError(f"feature_selection.lagged_corr must be of type LaggedCorrConfig, got {type(self.lagged_corr)}")
            if self.noise_detection is not None and not isinstance(self.noise_detection, NoiseDetectionConfig):
                raise TypeError(f"feature_selection.noise_detection must be of type NoiseDetectionConfig, got {type(self.noise_detection)}")

            if self.importance_threshold is not None and not (isinstance(self.importance_threshold, float) and 0.0 <= self.importance_threshold <= 1.0):
                raise ValueError(f"feature_selection.importance_threshold ({self.importance_threshold}) must be a float between 0.0 and 1.0 (representing percentile)")

            if self.top_k is not None and (not isinstance(self.top_k, int) or self.top_k <= 0):
                raise ValueError(f"feature_selection.top_k must be a positive integer, got {self.top_k}")

        elif self.method is not None or self.top_k is not None or self.lagged_corr is not None or self.noise_detection is not None or self.importance_threshold is not None:
            # Feature selection is disabled, but related parameters are specified. This is likely a configuration error.
            error_msg = ("Feature selection is disabled (enable=False), but related parameters "
                         "(method, top_k, lagged_corr, noise_detection, importance_threshold) are specified. "
                         "Please either enable feature selection or remove these parameters.")
            logger.error(error_msg)
            raise ValueError(error_msg)

@dataclass
class FeaturesConfig(BaseConfig):
    """特征配置"""
    # 移除 Optional 和默认值
    enable_selection: bool
    correlation_threshold: float
    importance_threshold: float
    noise_detection: dict[str, Any]
    feature_importance: dict[str, Any]
    dimensionality_reduction: dict[str, Any]

    def __post_init__(self):
        """初始化特征配置，检查必需字段并在缺失时抛出异常"""
        logger = get_logger(__name__)

        # 检查必需字段
        missing_fields = []

        # 检查必需字段是否存在 (移除 is None)
        if not hasattr(self, 'enable_selection'):
            missing_fields.append('enable_selection')
        if not hasattr(self, 'correlation_threshold'):
            missing_fields.append('correlation_threshold')
        if not hasattr(self, 'importance_threshold'):
            missing_fields.append('importance_threshold')

        # 检查 noise_detection 结构
        if not hasattr(self, 'noise_detection') or not isinstance(self.noise_detection, dict):
            missing_fields.append('noise_detection (必须是字典)')
        elif "low_variance_threshold" not in self.noise_detection or "high_correlation_threshold" not in self.noise_detection:
            missing_fields.append('noise_detection 必须包含 low_variance_threshold 和 high_correlation_threshold 键')

        # 检查 feature_importance 结构
        if not hasattr(self, 'feature_importance') or not isinstance(self.feature_importance, dict):
            missing_fields.append('feature_importance (必须是字典)')
        elif "method" not in self.feature_importance or "n_repeats" not in self.feature_importance:
            missing_fields.append('feature_importance 必须包含 method 和 n_repeats 键')

        # 检查 dimensionality_reduction 结构
        if not hasattr(self, 'dimensionality_reduction') or not isinstance(self.dimensionality_reduction, dict):
            missing_fields.append('dimensionality_reduction (必须是字典)')
        elif "enable" not in self.dimensionality_reduction or "method" not in self.dimensionality_reduction or "n_components" not in self.dimensionality_reduction:
            missing_fields.append('dimensionality_reduction 必须包含 enable, method, 和 n_components 键')

        # 如果有缺失字段，抛出异常
        if missing_fields:
            error_msg = f"特征配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
