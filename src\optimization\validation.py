"""
参数优化配置验证模块。

提供了一系列验证函数，用于检查配置的完整性和有效性。
包括必需配置项的存在性检查、配置值的类型和范围验证等。
还包括参数一致性验证，确保实际使用的参数与设定的参数完全一致。
"""

from typing import Any

import optuna

from src.optimization.exceptions import (
    ConfigurationError,
    InvalidConfigValueError,
    MissingConfigError,
)
from src.utils.config_manager import ConfigManager
from src.utils.logger import LoggerFactory

# 获取日志记录器
logger = LoggerFactory().get_logger("ConfigValidator")


def validate_lr_balancer_config(config: Any) -> None:
    """
    验证学习率平衡器配置。

    Args:
        config: 配置对象，应该包含training.lr_balancer部分

    Raises:
        MissingConfigError: 缺少必需的配置项
        InvalidConfigValueError: 配置值无效
        ConfigurationError: 其他配置错误
    """
    if not hasattr(config, 'training'):
        raise MissingConfigError("缺少training配置部分")

    if not hasattr(config.training, 'lr_balancer'):
        raise MissingConfigError("缺少training.lr_balancer配置部分")

    lr_balancer = config.training.lr_balancer

    # 验证必需字段
    required_fields = ['min_lr', 'max_lr', 'target_ratio']
    for field in required_fields:
        if not hasattr(lr_balancer, field):
            raise MissingConfigError(f"缺少lr_balancer.{field}配置")

    # 验证数值范围
    if not isinstance(lr_balancer.min_lr, int | float) or lr_balancer.min_lr <= 0:
        raise InvalidConfigValueError(f"lr_balancer.min_lr必须是正数，当前值: {lr_balancer.min_lr}")

    if not isinstance(lr_balancer.max_lr, int | float) or lr_balancer.max_lr <= lr_balancer.min_lr:
        raise InvalidConfigValueError(f"lr_balancer.max_lr必须大于min_lr，当前值: {lr_balancer.max_lr}")

    if not isinstance(lr_balancer.target_ratio, int | float) or lr_balancer.target_ratio <= 0:
        raise InvalidConfigValueError(f"lr_balancer.target_ratio必须是正数，当前值: {lr_balancer.target_ratio}")


def validate_optimizer_config(config: Any) -> None:
    """
    验证优化器配置。

    Args:
        config: 配置对象，应该包含training.optimizer部分

    Raises:
        MissingConfigError: 缺少必需的配置项
        InvalidConfigValueError: 配置值无效
    """
    if not hasattr(config, 'training'):
        raise MissingConfigError("缺少training配置部分")

    if not hasattr(config.training, 'optimizer'):
        raise MissingConfigError("缺少training.optimizer配置部分")

    optimizer = config.training.optimizer

    # 检查是否是字典类型
    if isinstance(optimizer, dict):
        if 'weight_decay' not in optimizer:
            raise MissingConfigError("缺少optimizer.weight_decay配置")

        if not isinstance(optimizer['weight_decay'], int | float) or optimizer['weight_decay'] < 0:
            raise InvalidConfigValueError(f"optimizer.weight_decay必须是非负数，当前值: {optimizer['weight_decay']}")
    else:
        if not hasattr(optimizer, 'weight_decay'):
            raise MissingConfigError("缺少optimizer.weight_decay配置")

        if not isinstance(optimizer.weight_decay, int | float) or optimizer.weight_decay < 0:
            raise InvalidConfigValueError(f"optimizer.weight_decay必须是非负数，当前值: {optimizer.weight_decay}")


def validate_model_config(config: Any) -> None:
    """
    验证模型配置。

    Args:
        config: 配置对象，应该包含model部分

    Raises:
        MissingConfigError: 缺少必需的配置项
        InvalidConfigValueError: 配置值无效
    """
    if not hasattr(config, 'model'):
        raise MissingConfigError("缺少model配置部分")

    model = config.model

    # 验证基本参数
    required_fields = ['hidden_dim', 'noise_dim', 'dropout_rate']
    for field in required_fields:
        if not hasattr(model, field):
            raise MissingConfigError(f"缺少model.{field}配置")

    # 验证维度参数
    if not isinstance(model.hidden_dim, int) or model.hidden_dim <= 0:
        raise InvalidConfigValueError(f"model.hidden_dim必须是正整数，当前值: {model.hidden_dim}")

    if not isinstance(model.noise_dim, int) or model.noise_dim <= 0:
        raise InvalidConfigValueError(f"model.noise_dim必须是正整数，当前值: {model.noise_dim}")

    # 验证dropout rate
    if not isinstance(model.dropout_rate, float) or not 0 <= model.dropout_rate < 1:
        raise InvalidConfigValueError(f"model.dropout_rate必须在[0,1)范围内，当前值: {model.dropout_rate}")


def validate_training_config(config: Any) -> None:
    """
    验证训练相关配置。

    Args:
        config: 配置对象

    Raises:
        MissingConfigError: 缺少必需的配置项
        InvalidConfigValueError: 配置值无效
    """
    if not hasattr(config, 'training'):
        raise MissingConfigError("缺少training配置部分")

    training = config.training

    # 验证lambda_gp
    if not hasattr(training, 'lambda_gp'):
        raise MissingConfigError("缺少training.lambda_gp配置")

    if not isinstance(training.lambda_gp, int | float) or training.lambda_gp <= 0:
        raise InvalidConfigValueError(f"training.lambda_gp必须是正数，当前值: {training.lambda_gp}")


def validate_complete_config(config: Any) -> None:
    """
    验证完整配置。
    执行所有必需的配置验证。

    Args:
        config: 完整的配置对象

    Raises:
        ConfigurationError: 配置验证失败
    """
    try:
        validate_lr_balancer_config(config)
        validate_optimizer_config(config)
        validate_model_config(config)
        validate_training_config(config)
    except (MissingConfigError, InvalidConfigValueError) as e:
        raise ConfigurationError(f"配置验证失败: {e!s}")


def verify_parameter_consistency(trial: optuna.trial.Trial, config: ConfigManager) -> None:
    """
    严格验证参数一致性，确保实际使用的参数与设定的参数完全一致。
    如果发现任何不一致，立即抛出异常并终止程序。

    Args:
        trial: Optuna试验对象
        config: 配置管理器实例

    Raises:
        ConfigurationError: 当参数不一致时抛出
    """
    inconsistencies = []

    # 检查每个试验参数是否正确应用到配置中
    for param_name, param_value in trial.params.items():
        # 解析参数路径，例如 "model.capacity" -> ["model", "capacity"]
        path_parts = param_name.split('.')

        # 获取实际配置中的值
        current_obj = config
        try:
            for i, part in enumerate(path_parts):
                if i == len(path_parts) - 1:
                    # 最后一个部分，检查值
                    if hasattr(current_obj, part):
                        actual_value = getattr(current_obj, part)
                    elif isinstance(current_obj, dict) and part in current_obj:
                        actual_value = current_obj[part]
                    else:
                        inconsistencies.append(f"参数路径 '{param_name}' 在配置中不存在")
                        break

                    # 检查值是否一致
                    if actual_value != param_value:
                        inconsistencies.append(
                            f"参数 '{param_name}' 值不一致: 期望 {param_value}, 实际 {actual_value}"
                        )
                # 中间路径部分
                elif hasattr(current_obj, part):
                    current_obj = getattr(current_obj, part)
                elif isinstance(current_obj, dict) and part in current_obj:
                    current_obj = current_obj[part]
                else:
                    inconsistencies.append(f"参数路径 '{param_name}' 中的 '{part}' 在配置中不存在")
                    break
        except Exception as e:
            inconsistencies.append(f"检查参数 '{param_name}' 时出错: {e!s}")

    # 检查嵌入维度与注意力头数的兼容性
    if hasattr(config, 'model'):
        # 安全地检查use_self_attention属性
        use_self_attention = False
        # 使用getattr避免直接访问可能不存在的属性
        use_self_attention = bool(getattr(config.model, 'use_self_attention', False))

        if use_self_attention:
            # 安全地获取hidden_dim和n_heads
            hidden_dim = None
            n_heads = None

            # 使用getattr避免直接访问可能不存在的属性
            hidden_dim = getattr(config.model, 'hidden_dim', None)
            n_heads = getattr(config.model, 'n_heads', None)

            # 只有当两个值都不为None时才进行检查
            if hidden_dim is not None and n_heads is not None:
                try:
                    if int(hidden_dim) % int(n_heads) != 0:
                        inconsistencies.append(f"嵌入维度 {hidden_dim} 不能被注意力头数 {n_heads} 整除")
                        logger.error(f"Trial #{trial.number}: 参数不兼容 - 嵌入维度 {hidden_dim} 不能被注意力头数 {n_heads} 整除")
                except (TypeError, ValueError) as e:
                    inconsistencies.append(f"检查嵌入维度与注意力头数兼容性时出错: {e!s}")
                    logger.error(f"Trial #{trial.number}: 检查嵌入维度与注意力头数兼容性时出错: {e!s}")

    # 如果发现任何不一致，抛出异常
    if inconsistencies:
        error_msg = "参数一致性检查失败:\n" + "\n".join(f"- {item}" for item in inconsistencies)
        logger.error(error_msg)
        raise ConfigurationError(error_msg)

    logger.info(f"Trial #{trial.number}: 参数一致性检查通过，所有参数已正确应用")
