"""重新设计的损失计算器模块 - 基于WGAN-GP的统一损失函数设计

核心设计原则：
1. 统一使用Wasserstein GAN with Gradient Penalty (WGAN-GP)
2. 简化损失组合，使用固定权重避免复杂调优
3. 确保数值稳定性，移除所有人为限制
4. 让模型自然学习，不干预正常的损失值

损失函数架构：
- 判别器损失 = Wasserstein损失 + 梯度惩罚
- 生成器损失 = 对抗损失 + 特征匹配损失 + 时序一致性损失

固定权重策略：
- 对抗损失权重: 1.0 (主导)
- 特征匹配损失权重: 1.0 (同等重要)
- 时序一致性损失权重: 0.1 (辅助约束)
- 梯度惩罚权重: 由配置动态传入
"""

import torch
from torch import nn

from src.utils.config_manager import ConfigManager
from src.utils.logger import LoggerFactory
from src.models.gan.feature_matching import FeatureMatchingLoss # 移动导入到文件顶部


class LossCalculator:
    """重新设计的损失计算器 - 基于WGAN-GP的统一损失函数设计"""

    # 固定权重策略 - 避免复杂的权重调优
    LOSS_WEIGHTS = {
        'adversarial': 1.0,           # 对抗损失权重 (主导)
        'feature_matching': 1.0,      # 特征匹配损失权重 (同等重要)
        'temporal_consistency': 0.1,  # 时序一致性损失权重 (辅助约束)
    }

    def __init__(self, config: ConfigManager, discriminator=None):
        """初始化损失计算器

        Args:
            config: 配置管理器
            discriminator: 判别器实例
        """
        self.logger = LoggerFactory().get_logger(self.__class__.__name__)
        self.config = config
        self.discriminator = discriminator

        # 直接使用固定权重策略
        self.lambda_feat = self.LOSS_WEIGHTS['feature_matching']
        self.lambda_temp = self.LOSS_WEIGHTS['temporal_consistency']
        self.lambda_adv = self.LOSS_WEIGHTS['adversarial']

        # 初始化基础损失函数
        self.mse_loss = nn.MSELoss()
        self.l1_loss = nn.L1Loss()

        self.logger.info(
            f"损失计算器初始化完成 (使用固定权重策略):\n"
            f"- 对抗损失权重: {self.lambda_adv}\n"
            f"- 特征匹配权重: {self.lambda_feat}\n"
            f"- 时序一致性权重: {self.lambda_temp}\n"
            f"- 梯度惩罚权重: 由训练器动态传入"
        )

    def compute_generator_loss(
        self,
        fake_sequence: torch.Tensor,
        real_sequence: torch.Tensor,
        fake_scores: torch.Tensor,
        condition_features: torch.Tensor
    ) -> dict[str, torch.Tensor]:
        """计算生成器损失 - 基于新的WGAN-GP设计

        目标: 欺骗判别器 + 保持特征相似性 + 保持时序连续性

        Args:
            fake_sequence: 生成的序列
            real_sequence: 真实序列
            fake_scores: 判别器对生成序列的评分张量
            condition_features: 条件特征

        Returns:
            Dict[str, torch.Tensor]: 包含总损失和各分量损失的字典
        """
        # 1. 对抗损失 - 希望判别器将生成样本判断为真实 (负的平均得分)
        # 移除所有人为限制，让模型自然学习
        adversarial_loss = -torch.mean(fake_scores)

        # 2. 特征匹配损失 - 生成样本与真实样本的特征分布相似
        feature_matching_loss = self._compute_feature_matching_loss(
            fake_sequence, real_sequence, condition_features
        )

        # 3. 时序一致性损失 - 保持生成序列的平滑性
        temporal_consistency_loss = self._compute_temporal_consistency_loss(fake_sequence)

        # 4. 组合损失 (使用固定权重，避免复杂的权重调优)
        total_loss = (
            self.lambda_adv * adversarial_loss +
            self.lambda_feat * feature_matching_loss +
            self.lambda_temp * temporal_consistency_loss
        )

        # 记录损失分解 (仅用于调试，不影响训练)
        self.logger.debug(
            f"生成器损失分解 (新设计):\n"
            f"- 对抗损失: {adversarial_loss.item():.4f} (权重: {self.lambda_adv})\n"
            f"- 特征匹配损失: {feature_matching_loss.item():.4f} (权重: {self.lambda_feat})\n"
            f"- 时序一致性损失: {temporal_consistency_loss.item():.4f} (权重: {self.lambda_temp})\n"
            f"- 总损失: {total_loss.item():.4f}"
        )

        return {
            'total_g_loss': total_loss,
            'g_loss_adv': adversarial_loss,
            'g_loss_fm': feature_matching_loss,
            'g_loss_temporal': temporal_consistency_loss
        }

    def compute_discriminator_loss(
        self,
        real_sequence: torch.Tensor,
        fake_sequence: torch.Tensor,
        real_scores: torch.Tensor,
        fake_scores: torch.Tensor,
        condition_features: torch.Tensor,
        current_lambda_gp: float
    ) -> dict[str, torch.Tensor]:
        """计算判别器损失 - 基于新的WGAN-GP设计

        目标: 最大化真实样本得分，最小化生成样本得分，同时满足Lipschitz约束

        Args:
            real_sequence: 真实序列
            fake_sequence: 生成的序列
            real_scores: 判别器对真实序列的评分张量
            fake_scores: 判别器对生成序列的评分张量
            condition_features: 条件特征 (用于梯度惩罚)
            current_lambda_gp: 当前的梯度惩罚权重

        Returns:
            Dict[str, torch.Tensor]: 包含总损失和各分量损失的字典
        """
        # 1. Wasserstein损失: 希望真实样本得分高，生成样本得分低
        wasserstein_loss = torch.mean(fake_scores) - torch.mean(real_scores)

        # 2. 梯度惩罚: 确保判别器满足1-Lipschitz约束
        gradient_penalty = self._compute_gradient_penalty(
            real_sequence, fake_sequence, condition_features
        )

        # 3. 总损失: Wasserstein损失 + 梯度惩罚
        total_loss = wasserstein_loss + current_lambda_gp * gradient_penalty

        # 记录损失分解 (仅用于调试，不影响训练)
        self.logger.debug(
            f"判别器损失分解 (新设计):\n"
            f"- Wasserstein损失: {wasserstein_loss.item():.4f}\n"
            f"- 梯度惩罚: {gradient_penalty.item():.4f} (权重: {current_lambda_gp})\n"
            f"- 总损失: {total_loss.item():.4f}"
        )

        return {
            'total_d_loss': total_loss,
            'wasserstein_loss': wasserstein_loss,
            'gradient_penalty': gradient_penalty,
            # 保持向后兼容性
            'd_loss_real': -torch.mean(real_scores),
            'd_loss_fake': torch.mean(fake_scores),
            'd_loss_gp': gradient_penalty
        }

    def _compute_gradient_penalty(
        self,
        real_sequence: torch.Tensor,
        fake_sequence: torch.Tensor,
        condition_features: torch.Tensor # 新增参数
    ) -> torch.Tensor:
        """计算梯度惩罚

        Args:
            real_sequence: 真实序列
            fake_sequence: 生成的序列
            condition_features: 条件特征

        Returns:
            torch.Tensor: 梯度惩罚
        """
        if self.discriminator is None:
            self.logger.warning("计算梯度惩罚时判别器为None")
            return torch.tensor(0.0, device=real_sequence.device)

        batch_size = real_sequence.size(0)
        device = real_sequence.device

        # 内存优化：使用较小的批次计算梯度惩罚
        # 进一步减小子批次大小，从8减小到4，降低内存需求
        max_batch_size = 4  # 设置更小的最大批次大小
        if batch_size > max_batch_size:
            # 分批计算梯度惩罚
            gradient_penalties = []
            for i in range(0, batch_size, max_batch_size):
                end_idx = min(i + max_batch_size, batch_size)
                sub_batch_size = end_idx - i

                # 获取当前批次的数据
                real_sub = real_sequence[i:end_idx]
                fake_sub = fake_sequence[i:end_idx]
                condition_sub = condition_features[i:end_idx]

                # 创建插值样本
                alpha = torch.rand(sub_batch_size, 1, 1, device=device)
                alpha = alpha.expand_as(real_sub)

                # 插值 target_sequence
                interpolated_target = alpha * real_sub + (1 - alpha) * fake_sub
                interpolated_target.requires_grad_(True)

                # 更积极地释放中间结果
                del alpha
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

                # 计算判别器对插值样本的评分
                interpolated_scores = self.discriminator(interpolated_target, condition_sub)

                # 更积极地释放中间结果
                del real_sub, fake_sub, condition_sub
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

                # 计算梯度
                gradients = torch.autograd.grad(
                    outputs=interpolated_scores,
                    inputs=interpolated_target,
                    grad_outputs=torch.ones_like(interpolated_scores, device=device),
                    create_graph=True,
                    retain_graph=True
                )[0]

                # 更积极地释放中间结果
                del interpolated_scores
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

                # 确保使用float32类型进行计算，避免混合精度训练中的数值问题
                gradients_float32 = gradients.to(torch.float32)

                # 检查梯度是否包含NaN或Inf
                if torch.isnan(gradients_float32).any() or torch.isinf(gradients_float32).any():
                    self.logger.error("梯度惩罚计算中梯度包含NaN/Inf，抛出异常以暴露问题")
                    raise ValueError("Gradient penalty computation resulted in NaN or Inf gradients.")
                else:
                    # 计算梯度范数 - 使用reshape而不是view，避免内存布局问题
                    # 使用float32类型进行计算
                    gradients_reshaped = gradients_float32.reshape(sub_batch_size, -1)

                    # 使用clamp避免极端值
                    gradients_clamped = torch.clamp(gradients_reshaped, -1e6, 1e6)
                    gradient_norm = gradients_clamped.norm(2, dim=1)

                    # 计算梯度惩罚
                    sub_gradient_penalty = torch.mean((gradient_norm - 1) ** 2)
                gradient_penalties.append(sub_gradient_penalty)

                # 主动释放不再需要的中间结果
                del interpolated_target, gradients
                # 不再尝试删除可能不存在的变量
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

                # 强制垃圾回收
                import gc
                gc.collect()

            # 计算所有批次的平均梯度惩罚
            gradient_penalty = torch.mean(torch.stack(gradient_penalties))
        else:
            # 对于小批次，直接计算
            # 创建插值样本
            alpha = torch.rand(batch_size, 1, 1, device=device)
            alpha = alpha.expand_as(real_sequence)

            # 插值 target_sequence
            interpolated_target = alpha * real_sequence + (1 - alpha) * fake_sequence
            interpolated_target.requires_grad_(True)

            # 更积极地释放中间结果
            del alpha
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            # 计算判别器对插值样本的评分
            interpolated_scores = self.discriminator(interpolated_target, condition_features)

            # 更积极地释放中间结果
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            # 计算梯度
            gradients = torch.autograd.grad(
                outputs=interpolated_scores,
                inputs=interpolated_target,
                grad_outputs=torch.ones_like(interpolated_scores, device=device),
                create_graph=True,
                retain_graph=True
            )[0]

            # 更积极地释放中间结果
            del interpolated_scores
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            # 确保使用float32类型进行计算，避免混合精度训练中的数值问题
            gradients_float32 = gradients.to(torch.float32)

            # 检查梯度是否包含NaN或Inf
            if torch.isnan(gradients_float32).any() or torch.isinf(gradients_float32).any():
                self.logger.error("梯度惩罚计算中梯度包含NaN/Inf，抛出异常以暴露问题")
                raise ValueError("Gradient penalty computation resulted in NaN or Inf gradients.")
            else:
                # 计算梯度范数 - 使用reshape而不是view，避免内存布局问题
                # 使用float32类型进行计算
                gradients_reshaped = gradients_float32.reshape(batch_size, -1)

                # 使用clamp避免极端值
                gradients_clamped = torch.clamp(gradients_reshaped, -1e6, 1e6)
                gradient_norm = gradients_clamped.norm(2, dim=1)

                # 计算梯度惩罚
                gradient_penalty = torch.mean((gradient_norm - 1) ** 2)

            # 主动释放不再需要的中间结果
            del interpolated_target, gradients
            # 不再尝试删除可能不存在的变量
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            # 强制垃圾回收
            import gc
            gc.collect()

        return gradient_penalty

    def _compute_feature_matching_loss(
        self,
        fake_sequence: torch.Tensor,
        real_sequence: torch.Tensor,
        condition_features: torch.Tensor
    ) -> torch.Tensor:
        """计算特征匹配损失 - 基于新的设计

        特征匹配损失: 使生成样本的中间特征与真实样本相似

        Args:
            fake_sequence: 生成的序列
            real_sequence: 真实序列
            condition_features: 条件特征

        Returns:
            torch.Tensor: 特征匹配损失
        """
        # 如果判别器不可用，使用简单的MSE损失作为后备
        if self.discriminator is None:
            self.logger.warning("判别器不可用，使用MSE损失作为特征匹配损失")
            return self.mse_loss(fake_sequence, real_sequence)

        # 提取真实样本的中间特征 (参与梯度计算，修复之前的设计错误)
        real_features = self.discriminator.extract_features(real_sequence, condition_features)

        # 提取生成样本的中间特征
        fake_features = self.discriminator.extract_features(fake_sequence, condition_features)

        # 实例化 FeatureMatchingLoss 并计算损失
        feature_matching_calculator = FeatureMatchingLoss()
        feature_loss = feature_matching_calculator(real_features, fake_features)

        return feature_loss

    def _compute_temporal_consistency_loss(
        self,
        sequence: torch.Tensor
    ) -> torch.Tensor:
        """计算时序一致性损失 - 基于新的设计

        时序一致性损失: 保持生成序列的平滑性和连续性

        Args:
            sequence: 生成的序列

        Returns:
            torch.Tensor: 时序一致性损失
        """
        # 计算相邻时间点的差异 (一阶差分)
        first_diff = sequence[:, 1:, :] - sequence[:, :-1, :]

        # 计算二阶差分 (变化率的变化)
        second_diff = first_diff[:, 1:, :] - first_diff[:, :-1, :]

        # 时序一致性损失 = 一阶差分的L1范数 + 二阶差分的L1范数
        first_order_loss = torch.mean(torch.abs(first_diff))
        second_order_loss = torch.mean(torch.abs(second_diff))

        # 组合损失 (二阶差分权重较小，主要约束平滑性)
        temporal_loss = first_order_loss + 0.1 * second_order_loss

        return temporal_loss
