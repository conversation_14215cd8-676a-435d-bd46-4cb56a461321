"""特征管理器 - 协调各个特征生成器

此模块负责协调各个特征生成器，生成完整的特征集。它是特征工程系统的核心组件，
替代了原来的FeatureEngineer类，提供了更加模块化和可扩展的设计。
"""

import time
from typing import Any

import numpy as np
import pandas as pd
import torch

from src.data.feature_engineering.base import BaseFeatureGenerator
from src.data.feature_engineering.diff_features import DiffFeatureGenerator
from src.data.feature_engineering.interaction_features import (
    InteractionFeatureGenerator,
)
from src.data.feature_engineering.lag_features import LagFeatureGenerator
from src.data.feature_engineering.time_features import TimeFeatureGenerator
from src.data.feature_engineering.volatility_features import VolatilityFeatureGenerator
from src.data.feature_engineering.window_features import WindowFeatureGenerator
from src.utils.config.manager import ConfigManager
from src.utils.logger import get_logger


class FeatureManager:
    """特征管理器

    负责协调各个特征生成器，生成完整的特征集
    """

    def __init__(self, config: ConfigManager, logger=None):
        """初始化特征管理器

        Args:
            config: 配置管理器实例
            logger: 日志记录器实例，如果为None则使用默认LoggerFactory
        """
        if not isinstance(config, ConfigManager):
            raise TypeError("config参数必须为ConfigManager实例")

        self.config = config
        self.logger = logger if logger is not None else get_logger(__name__)

        # 初始化时间特征预处理器 (独立于层级生成器)
        self.time_preprocessor = TimeFeatureGenerator(self.config)

        # 初始化层级特征生成器
        self.generators: dict[str, BaseFeatureGenerator] = {}
        self._initialize_generators() # 这个方法现在只初始化层级生成器

        # 特征统计信息
        self.feature_counts: dict[str, int] = {}
        self.generated_feature_counts: dict[str, int] = {}

        # 初始化编码器参数
        self.feature_dim = None
        self.numeric_features = []
        self.categorical_features = []
        self.temporal_features = []

        # 配置编码器
        self._configure_encoder()

        self.logger.info("特征管理器初始化完成")

    def _initialize_generators(self):
        """初始化所有特征生成器"""
        # 检查特征工程配置
        if not hasattr(self.config, 'feature_engineering'):
            error_msg = "配置中缺少 feature_engineering 配置项"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        feature_eng = self.config.feature_engineering

        # 检查全局开关
        if not hasattr(feature_eng, 'enable'):
            error_msg = "配置中缺少 feature_engineering.enable 配置项"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if not feature_eng.enable:
            # 当全局开关关闭时，明确记录哪些类型的生成器不会被初始化
            # 由于此时 self.generators 尚未填充具体的实例，我们基于设计意图来记录
            potential_generators = ['diff', 'lag', 'window', 'volatility', 'interaction']
            self.logger.info(
                f"特征工程全局开关已关闭。因此，以下层级特征生成器类型将不会被初始化: "
                f"{', '.join(potential_generators)}. "
                f"时间特征预处理器 (TimeFeatureGenerator) 的初始化不受此全局开关直接控制，其启用状态独立判断。"
            )
            return

        # 初始化各个层级特征生成器 (不再包含 time)
        # self.generators['time'] = TimeFeatureGenerator(self.config) # 已移到 __init__
        self.generators['diff'] = DiffFeatureGenerator(self.config)
        self.generators['lag'] = LagFeatureGenerator(self.config)
        self.generators['window'] = WindowFeatureGenerator(self.config)
        self.generators['volatility'] = VolatilityFeatureGenerator(self.config)
        self.generators['interaction'] = InteractionFeatureGenerator(self.config)

        # 记录启用的层级生成器
        enabled_generators = [name for name, gen in self.generators.items() if gen.is_enabled]
        self.logger.info(f"已初始化 {len(self.generators)} 个层级特征生成器，启用的生成器: {enabled_generators}")
        # 单独记录时间预处理器状态
        if self.time_preprocessor.is_enabled:
             self.logger.info("时间特征预处理器已启用。")
        else:
             self.logger.info("时间特征预处理器已禁用。")

    def _configure_encoder(self):
        """配置编码器参数"""
        if not hasattr(self.config, 'feature_engineering'):
            raise ValueError("ConfigManager实例缺少 feature_engineering 属性")

        feature_eng_cfg = self.config.feature_engineering

        # 检查列配置
        columns_cfg = getattr(feature_eng_cfg, 'columns', None)
        if not columns_cfg:
            raise ValueError("配置中缺少 feature_engineering.columns 配置项")

        columns_dict = columns_cfg if isinstance(columns_cfg, dict) else vars(columns_cfg)

        if 'time_features' not in columns_dict:
            raise ValueError("配置中必须显式指定 time_features 列")

        # 初始化特征列表
        self.numeric_features = []  # 将在运行时动态检测
        # Roo-Fix: 直接访问，如果 'categorical' 缺失则 ConfigManager/dict 会抛出 KeyError
        self.categorical_features = list(columns_dict['categorical'])
        self.temporal_features = list(columns_dict['time_features'])

        # 从数据配置中获取特征维度
        if hasattr(self.config, 'data') and hasattr(self.config.data, 'feature_dim'):
            self.feature_dim = self.config.data.feature_dim
        else:
            self.feature_dim = None

        # 记录配置详情
        self.logger.info(
            f"特征工程配置详情:\n"
            f"- 数值特征: 将在运行时动态检测\n"
            f"- 类别特征: {self.categorical_features}\n"
            f"- 时间特征: {self.temporal_features}\n"
            f"- 特征维度: {self.feature_dim}"
        )

        # 添加日志，提示用户数值特征将在运行时动态检测
        self.logger.info("数值特征列表为空，将在运行时动态检测所有数值列作为特征")

    def validate_input_data(self, data: pd.DataFrame) -> tuple[bool, str | None]:
        """验证输入数据的基本结构

        Args:
            data: 输入的DataFrame

        Returns:
            Tuple[bool, Optional[str]]: (验证是否通过, 错误信息)
        """
        try:
            # 1. 检查日期列
            date_col = None
            if not hasattr(self.config, 'feature_engineering'):
                return False, "配置中缺少 feature_engineering 配置项"

            feature_eng = self.config.feature_engineering

            if not hasattr(feature_eng, 'columns'):
                return False, "配置中缺少 feature_engineering.columns 配置项"

            columns_cfg = feature_eng.columns
            columns_dict = columns_cfg if isinstance(columns_cfg, dict) else vars(columns_cfg)

            if 'time_features' not in columns_dict:
                return False, "配置中缺少 feature_engineering.columns.time_features 配置项"

            time_features = columns_dict['time_features']
            if not time_features:
                return False, "feature_engineering.columns.time_features 不能为空"

            if time_features[0] in data.columns:
                date_col = time_features[0]
                try:
                    pd.to_datetime(data[date_col])
                except Exception:
                    return False, f"日期列 '{date_col}' 无法转换为datetime格式"
            else:
                return False, f"日期列 '{time_features[0]}' 不存在于输入数据中"

            # 2. 检查目标列
            if not hasattr(self.config, 'data'):
                return False, "配置中缺少 data 配置项"

            if not hasattr(self.config.data, 'target'):
                return False, "配置中缺少 data.target 配置项"

            target_col = self.config.data.target
            if not target_col or target_col not in data.columns:
                return False, f"目标列 '{target_col}' 不存在"
            if not pd.api.types.is_numeric_dtype(data[target_col]):
                return False, f"目标列 '{target_col}' 不是数值类型"

            # 3. 检查特征列
            other_cols = [col for col in data.columns if col not in [date_col, target_col]]
            for col in other_cols:
                if not pd.api.types.is_numeric_dtype(data[col]):
                    return False, f"特征列 '{col}' 不是数值类型"

            return True, None

        except Exception as e:
            return False, f"数据验证失败: {e!s}"

    def process(self, data: np.ndarray | pd.DataFrame | torch.Tensor | tuple) -> pd.DataFrame:
        """处理数据并生成特征

        Args:
            data: 输入数据，支持以下格式：
                - pd.DataFrame: 包含日期列、特征列和目标列
                - numpy.ndarray: 纯数值特征数组
                - torch.Tensor: 纯数值特征张量
                - tuple: (features, targets) 格式的数据

        Returns:
            pd.DataFrame: 处理后的特征数据框
        """
        self.logger.info(f"开始特征工程处理 - 输入类型: {type(data).__name__}")
        processing_start = time.time()

        try:
            # 1. 数据预处理
            features_data = None
            date_series = None
            initial_numeric_cols = [] # 存储原始数值列名
            target_data_series: pd.Series | None = None # 初始化目标序列

            if isinstance(data, tuple) and len(data) >= 1:
                self.logger.info("检测到元组输入(features, targets)")
                features_data = data[0]
                # 注意：我们不需要处理目标值，只关注特征
            else:
                features_data = data

            # 2. DataFrame处理
            if isinstance(features_data, pd.DataFrame):
                # 数据验证
                valid, error_msg = self.validate_input_data(features_data)
                if not valid:
                    raise ValueError(f"输入数据验证失败: {error_msg}")

                # 记录原始信息
                self.logger.info(
                    f"原始数据信息:\n"
                    f"- 维度: {features_data.shape}\n"
                    f"- 列名: {features_data.columns.tolist()}\n"
                    f"- 数据类型:\n{features_data.dtypes}"
                )

                # 3. 分离特征
                # 获取时间特征列名 (通常只有一个)
                time_col_names = self.temporal_features
                if not time_col_names:
                    error_msg = "未找到时间特征列配置，请在配置文件中指定 feature_engineering.columns.time_features"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)
                date_col = time_col_names[0] # 假设第一个是主日期列

                # 获取目标列
                if not hasattr(self.config, 'data') or not hasattr(self.config.data, 'target'):
                    error_msg = "配置中未指定目标列，请在配置文件中指定 data.target"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)
                target_col = self.config.data.target
                if not target_col:
                    error_msg = "配置中的目标列为空，请指定有效的目标列名"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)

                # 分离日期列
                if date_col not in features_data.columns:
                    error_msg = f"日期列 '{date_col}' 不存在于输入数据中"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)
                try:
                    date_series = pd.to_datetime(features_data[date_col])
                    self.logger.info(f"成功提取日期列: {date_col}")
                except Exception as e:
                    error_msg = f"日期列 '{date_col}' 转换失败: {e!s}"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg) from e

                # 分离目标列 (如果存在)
                if target_col in features_data.columns:
                     target_data_series = features_data[target_col].copy() # 获取 pd.Series
                     features_data = features_data.drop(columns=[target_col])
                     self.logger.info(f"已从特征数据中分离目标列: {target_col}")
                else:
                     self.logger.warning(f"目标列 '{target_col}' 不存在于输入数据中，继续处理剩余列。")
                     # target_data_series 保持为 None


                # 提取原始数值特征列
                numeric_cols = []
                # Roo-Fix: 明确排除已知的类别特征
                known_categorical = set(self.categorical_features)
                potential_feature_cols = [
                    col for col in features_data.columns
                    if col not in (date_col, target_col) and col not in known_categorical
                ]
                self.logger.info(f"潜在数值特征列 (排除日期/目标/已知类别): {potential_feature_cols}")

                for col in potential_feature_cols:
                    # Roo-Fix: 如果列类型不是数值，直接抛出错误，不再尝试转换或警告
                    if not pd.api.types.is_numeric_dtype(features_data[col]):
                        error_msg = (f"列 '{col}' 的数据类型不是数值类型 ({features_data[col].dtype})，"
                                     f"FeatureManager 期望所有非日期/目标/类别列都为数值类型。")
                        self.logger.error(error_msg)
                        raise TypeError(error_msg)
                    else:
                        numeric_cols.append(col)
                        # self.logger.debug(f"列 '{col}' 是数值类型，已添加到数值特征列表。") # 可选的调试日志

                if not numeric_cols:
                    error_msg = "未检测到任何有效的原始数值特征列 (在排除日期/目标/类别后)"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)

                initial_numeric_cols = numeric_cols # 保存原始数值列名
                self.numeric_features = initial_numeric_cols # 更新管理器状态
                self.logger.info(f"提取到的原始数值特征列: {initial_numeric_cols}")

                # 准备原始数值张量
                numeric_data_df = features_data[initial_numeric_cols].copy()
                # 确保类型正确并处理可能的错误
                for col in initial_numeric_cols:
                     # Roo-Fix: 使用 errors='raise' 并在转换失败时抛出更具体的错误
                     try:
                         # 尝试强制转换为数值类型，如果失败则会抛出异常
                         numeric_data_df[col] = pd.to_numeric(numeric_data_df[col], errors='raise')
                     except (ValueError, TypeError) as e:
                         error_msg = f"无法将列 '{col}' 中的值转换为数值类型: {e!s}"
                         self.logger.error(error_msg)
                         raise ValueError(error_msg) from e

                # 检查是否有 NaN (理论上 errors='raise' 应该已经捕获了非数值，但以防万一)
                if numeric_data_df.isna().values.any():
                     na_cols = numeric_data_df.columns[numeric_data_df.isna().any()].tolist()
                     # 这通常不应该发生，因为 errors='raise' 会先失败
                     self.logger.warning(f"警告：在强制数值转换后仍然检测到 NaN 值，问题列: {na_cols}。这可能表示原始数据中存在 NaN。")
                     # 根据需要决定是否抛出错误，这里暂时保留警告
                     # raise ValueError(f"原始数值特征在转换后仍包含无效值 (NaN)，问题列: {na_cols}")

                initial_numeric_tensor = torch.tensor(numeric_data_df.values, dtype=torch.float32)


                # 4. 时间特征预处理
                time_features_tensor = torch.empty((initial_numeric_tensor.shape[0], 0), dtype=torch.float32)
                time_feature_names = []
                if self.time_preprocessor.is_enabled:
                    self.logger.info("调用 TimeFeatureGenerator.generate() 处理时间序列数据...")
                    time_features_tensor, time_feature_names = self.time_preprocessor.generate(date_series)
                    self.logger.info(f"TimeFeatureGenerator.generate() 返回 {time_features_tensor.shape[1]} 个时间特征: {time_feature_names}")
                    # 确保设备一致性 (如果需要)
                    # time_features_tensor = time_features_tensor.to(initial_numeric_tensor.device)
                else:
                    self.logger.info("时间特征预处理已禁用。")

                # 5. 合并原始数值特征和时间特征 -> Level 0 输入
                level0_input_tensor = torch.cat([initial_numeric_tensor, time_features_tensor], dim=1)
                # 计算特征名称列表 (仅用于日志记录)
                _ = initial_numeric_cols + time_feature_names  # 未使用的表达式，仅用于计算
                self.logger.info(f"Level 0 特征准备完成 | 维度: {level0_input_tensor.shape} | "
                                 f"构成: {len(initial_numeric_cols)} 原始数值 + {len(time_feature_names)} 时间特征")


                # 6. 执行层级特征增强
                # 注意：现在 enhance_features 不再需要 date_series
                # enhance_features 返回 (特征张量, 衍生特征名称列表)
                enhanced_features_tensor, enhanced_feature_names = self.enhance_features(
                    level0_input_tensor,
                    target_series=target_data_series # 新增传递
                )
                self.logger.info(f"enhance_features 返回 {len(enhanced_feature_names)} 个衍生特征名称")

                # 7. 组合最终特征名称列表
                final_feature_names = []
                # final_combined_tensor_list = [] # 不再需要手动收集张量列表

                # Roo-Fix: 直接访问，如果缺失 ConfigManager/AttributeError 会抛出
                keep_original_in_final = self.config.feature_engineering.keep_original_in_final

                # enhanced_features_tensor 已经根据 keep_original_in_final 包含了 Level 0 特征 (如果需要)
                # enhanced_feature_names 只包含衍生特征的名称

                if keep_original_in_final:
                    # 如果要保留原始特征，名称列表需要包含 Level 0 的名称
                    final_feature_names.extend(initial_numeric_cols)
                    final_feature_names.extend(time_feature_names)
                    self.logger.info(f"最终特征集将包含 Level 0 特征名称 ({len(initial_numeric_cols)} 原始 + {len(time_feature_names)} 时间)")
                    # 张量部分将由 enhanced_features_tensor 提供，它已经包含了 Level 0
                else:
                    self.logger.info("最终特征集不包含 Level 0 特征名称")

                # 添加 enhance_features 生成的衍生特征的名称
                final_feature_names.extend(enhanced_feature_names)

                # 最终的张量直接使用 enhance_features 返回的张量，
                # 因为它已经根据 keep_original_in_final 正确组合了 Level 0 和衍生特征
                final_combined_tensor = enhanced_features_tensor

                # 检查是否有特征生成 (名称列表的检查)
                if not final_feature_names:
                     error_msg = "特征工程未产生任何最终特征名称，请检查配置和实现"
                     self.logger.error(error_msg)
                     raise ValueError(error_msg)
                # 张量本身的检查 (例如是否为 None 或空)
                if final_combined_tensor is None or final_combined_tensor.shape[0] == 0 or final_combined_tensor.shape[1] == 0 : # 检查行数和列数
                     error_msg = "特征工程未产生任何最终特征张量 (可能为空或维度为0)，请检查配置和实现"
                     self.logger.error(error_msg)
                     raise ValueError(error_msg)

                # 检查最终张量和名称列表的维度是否匹配
                if final_combined_tensor.shape[1] != len(final_feature_names):
                    mismatch_msg = (f"最终特征张量维度 ({final_combined_tensor.shape[1]}) 与 "
                                    f"特征名称数量 ({len(final_feature_names)}) 不匹配！")
                    self.logger.error(mismatch_msg)
                    # 记录详细信息以供调试
                    level0_names_for_log = initial_numeric_cols + time_feature_names if keep_original_in_final else 'Not Kept'
                    self.logger.error(f"Level 0 Names (for final_feature_names list if kept) ({len(level0_names_for_log) if isinstance(level0_names_for_log, list) else 0}): {level0_names_for_log}")
                    self.logger.error(f"Enhanced (Derived) Names ({len(enhanced_feature_names)}): {enhanced_feature_names}")
                    self.logger.error(f"Total names in final_feature_names: {len(final_feature_names)}")
                    self.logger.error(f"Shape of final_combined_tensor (from enhance_features): {final_combined_tensor.shape}")
                    raise ValueError(mismatch_msg)

                # 8. 生成最终结果DataFrame
                result_df = pd.DataFrame(
                    final_combined_tensor.cpu().numpy(), # 确保在CPU上
                    columns=pd.Index(final_feature_names), # 使用正确的名称
                    index=date_series.index # 保留原始索引
                )

                # 记录结果信息
                processing_time = time.time() - processing_start
                self.logger.info(
                    f"\n特征工程完成:\n"
                    f"- 处理时间: {processing_time:.2f}秒\n"
                    f"- Level 0 输入维度: {level0_input_tensor.shape}\n"
                    f"- 最终输出维度: {result_df.shape}\n"
                    f"- 最终特征数量: {len(result_df.columns)}"
                )

                return result_df

            else:
                # 非DataFrame输入的处理 (不支持时间特征预处理)
                self.logger.warning("输入不是DataFrame，无法执行时间特征预处理。")
                if isinstance(features_data, np.ndarray):
                    tensor_data = torch.tensor(features_data, dtype=torch.float32)
                elif isinstance(features_data, torch.Tensor):
                    tensor_data = features_data
                else:
                    raise TypeError(f"不支持的输入类型: {type(features_data)}")

                # 直接将输入传递给层级增强
                # 注意：非DataFrame输入无法获取原始列名或生成时间特征名
                # enhance_features 返回 (特征张量, 衍生特征名称列表)
                # 对于非DataFrame输入，通常没有明确的目标序列可以传递
                enhanced_features_tensor, enhanced_feature_names = self.enhance_features(tensor_data, target_series=None)
                self.logger.info(f"enhance_features (非DataFrame输入) 返回 {len(enhanced_feature_names)} 个衍生特征名称")

                # 对于非DataFrame输入，我们通常只关心 enhance_features 返回的衍生特征
                # 如果需要保留原始输入，需要更复杂的处理（例如假设通用名称）
                # 这里我们假设只使用衍生特征
                final_feature_names = enhanced_feature_names
                final_combined_tensor = enhanced_features_tensor

                if final_combined_tensor.shape[1] != len(final_feature_names):
                     mismatch_msg = (f"最终特征张量维度 ({final_combined_tensor.shape[1]}) 与 "
                                     f"特征名称数量 ({len(final_feature_names)}) 不匹配！(非DataFrame输入)")
                     self.logger.error(mismatch_msg)
                     raise ValueError(mismatch_msg)

                result_df = pd.DataFrame(
                    final_combined_tensor.cpu().numpy(),
                    columns=pd.Index(final_feature_names) # 使用 enhance_features 返回的名称
                )

                processing_time = time.time() - processing_start
                self.logger.info(
                    f"非DataFrame输入处理完成:\n"
                    f"- 处理时间: {processing_time:.2f}秒\n"
                    f"- 最终特征维度: {result_df.shape}"
                )

                return result_df

        except Exception as e:
            error_msg = f"特征工程处理失败: {e!s}"
            self.logger.error(f"{error_msg}\n调用栈信息:", exc_info=True)
            raise ValueError(error_msg) from e

    # 移除 date_series 参数，修改返回类型为 Tuple[torch.Tensor, List[str]]
    # 增加 target_series 参数
    def enhance_features(self, data: torch.Tensor, target_series: pd.Series | None = None) -> tuple[torch.Tensor, list[str]]:
        """执行层级特征增强流程 (金字塔模型)

        Args:
            data: 输入数据张量 (Level 0 - 已包含原始数值和时间特征)
            target_series: 可选的目标变量序列，用于某些需要它的生成器 (如 InteractionFeatureGenerator)

        Returns:
            Tuple[torch.Tensor, List[str]]:
                - torch.Tensor: 最终组合的特征张量 (L0 + Derived L1 + ...)
                - List[str]: 对应的最终特征名称列表
        """
        self.logger.info(f"开始层级特征增强 - 输入形状: {data.shape}")
        if target_series is not None:
            self.logger.info(f"目标序列已提供给 enhance_features，长度: {len(target_series)}")
        else:
            self.logger.info("目标序列未提供给 enhance_features。")
        processing_start = time.time()

        try:
            # --- 1. 输入检查和预处理 ---
            if data is None:
                error_msg = "输入数据不能为None"
                self.logger.error(f"enhance_features 输入检查失败: {error_msg}")
                raise ValueError(error_msg)
            if not isinstance(data, torch.Tensor):
                error_msg = f"输入数据类型错误，期望torch.Tensor，实际为{type(data)}"
                self.logger.error(f"enhance_features 输入检查失败: {error_msg}")
                raise TypeError(error_msg)
            if len(data.shape) != 2:
                error_msg = f"输入数据维度错误，期望2维，实际为{len(data.shape)}维"
                self.logger.error(f"enhance_features 输入检查失败: {error_msg}")
                raise ValueError(error_msg)
            if data.shape[0] == 0 or data.shape[1] == 0:
                error_msg = f"输入数据维度不能为0，实际形状为{data.shape}"
                self.logger.error(f"enhance_features 输入检查失败: {error_msg}")
                raise ValueError(error_msg)
            if torch.isnan(data).any():
                error_msg = "输入数据包含NaN值"
                self.logger.error(f"enhance_features 输入检查失败: {error_msg}")
                raise ValueError(error_msg)
            if torch.isinf(data).any():
                error_msg = "输入数据包含无穷大值"
                self.logger.error(f"enhance_features 输入检查失败: {error_msg}")
                raise ValueError(error_msg)

            # 记录输入数据信息
            stats = {
                "shape": data.shape, "dtype": data.dtype,
                "device": data.device if hasattr(data, 'device') else 'CPU',
                "min": data.min().item(), "max": data.max().item(),
                "mean": data.mean().item(), "std": data.std().item(),
                "non_zero": (data != 0).float().mean().item()
            }
            self.logger.info(
                f"\n=== 层级特征工程开始 (Level 0 Input) ==="
                f"\n- 输入形状: {stats['shape']}"
                f"\n- 数值范围: [{stats['min']:.2f}, {stats['max']:.2f}]"
                f"\n- 均值: {stats['mean']:.2f}, 标准差: {stats['std']:.2f}"
                f"\n- 非零比例: {stats['non_zero']:.2%}"
                f"\n- 设备: {stats['device']}"
            )

            # --- 2. 配置检查 ---
            if not hasattr(self.config, 'feature_engineering'):
                raise ValueError("配置中缺少 feature_engineering 配置项")
            feature_eng = self.config.feature_engineering
            if not hasattr(feature_eng, 'enable') or not feature_eng.enable:
                self.logger.info("特征工程全局开关已关闭，跳过特征工程步骤。")
                # 返回原始数据和空的特征名称列表，以匹配返回类型签名
                return data, []
            if not hasattr(feature_eng, 'layers') or not isinstance(feature_eng.layers, list):
                raise ValueError("配置中缺少有效的 feature_engineering.layers 列表")
            if not hasattr(feature_eng, 'keep_original_in_final'):
                 # Roo-Fix: 配置缺失时抛出错误，而不是警告和默认值
                 error_msg = "配置中缺少 feature_engineering.keep_original_in_final 配置项"
                 self.logger.error(error_msg)
                 raise ValueError(error_msg)
            else:
                 keep_original_in_final = feature_eng.keep_original_in_final

            # --- 3. 层级处理 ---
            final_feature_collection: list[torch.Tensor] = [] # 存储最终输出的特征 (L0 + Derived L1 + Derived L2 + ...)
            layer_derived_feature_counts: dict[int, dict[str, int]] = {} # 记录每层各生成器产生的特征数
            final_feature_names_collection: list[list[str]] = [] # 存储最终输出的特征名称列表

            current_layer_input: torch.Tensor = data # 当前层的输入，初始为 Level 0
            # 注意：此时 data 已经是 Level 0 (原始数值 + 时间特征)，其名称列表在 process 方法中处理
            original_features: torch.Tensor = data # 保留 Level 0 特征引用

            if keep_original_in_final:
                final_feature_collection.append(original_features)
                # Level 0 的名称将在 process 方法中处理并添加到最终列表
                self.logger.info(f"最终特征集将包含 Level 0 (原始+时间) 特征 | 维度: {original_features.shape}")
            else:
                 # 即使不保留Level 0到最终输出，它仍然是第一层的输入
                 pass


            # 按层级顺序处理
            # Roo-Fix: 直接访问 level，后续有检查
            sorted_layers = sorted(feature_eng.layers, key=lambda x: x.level)

            for layer_config in sorted_layers:
                # 修正：使用属性访问 (dot notation) 访问 LayerConfig 实例的字段
                # 检查 layer_config 是否真的是 LayerConfig 实例 (或兼容类型)
                if not hasattr(layer_config, 'level'):
                     # Roo-Fix: 配置缺失时抛出错误，而不是警告
                     error_msg = f"层配置对象缺少 'level' 属性: {layer_config}"
                     self.logger.error(error_msg)
                     raise ValueError(error_msg)
                     # self.logger.warning(f"层配置对象缺少 'level' 属性: {layer_config}，跳过")
                     # continue
                level = layer_config.level # 直接访问属性

                # 同样，直接访问其他属性，并提供默认值（如果属性可能不存在）
                # 但 LayerConfig 定义了这些属性，所以理论上可以直接访问
                layer_generators_config = layer_config.generators
                keep_input_features = layer_config.keep_input_features

                # 可以在这里添加更健壮的检查，例如检查类型
                # Roo-Fix: 类型错误时抛出 TypeError，而不是警告和跳过
                if not isinstance(level, int):
                     error_msg = f"层配置 'level' 类型错误 (期望 int): {type(level)}"
                     self.logger.error(error_msg)
                     raise TypeError(error_msg)
                     # self.logger.warning(f"层配置 'level' 类型错误 (期望 int): {type(level)}，跳过")
                     # continue
                if not isinstance(layer_generators_config, list):
                     error_msg = f"层配置 'generators' 类型错误 (期望 list): {type(layer_generators_config)}"
                     self.logger.error(error_msg)
                     raise TypeError(error_msg)
                     # self.logger.warning(f"层配置 'generators' 类型错误 (期望 list): {type(layer_generators_config)}，跳过")
                     # continue
                if not isinstance(keep_input_features, bool):
                     error_msg = f"层配置 'keep_input_features' 类型错误 (期望 bool): {type(keep_input_features)}"
                     self.logger.error(error_msg)
                     raise TypeError(error_msg)
                     # self.logger.warning(f"层配置 'keep_input_features' 类型错误 (期望 bool): {type(keep_input_features)}，跳过")
                     # continue


                self.logger.info(f"\n--- Processing Level {level} ---")
                self.logger.info(f"Level {level} Input Shape: {current_layer_input.shape}")

                current_layer_derived_features: list[torch.Tensor] = [] # 存储当前层新生成的特征张量
                current_layer_derived_names: list[str] = [] # 存储当前层新生成的特征名称
                next_layer_input_components: list[torch.Tensor] = [] # 构建下一层的输入张量
                # next_layer_input_names: List[str] = [] # 构建下一层的输入名称 (如果需要跟踪)

                layer_derived_feature_counts[level] = {} # 初始化当前层计数器

                if keep_input_features:
                    next_layer_input_components.append(current_layer_input)
                    # next_layer_input_names.extend(current_layer_input_names) # 需要跟踪上一层的名称
                    self.logger.info(f"Level {level}: 将保留输入特征 ({current_layer_input.shape[1]} 列) 到下一层输入")
                else:
                    self.logger.info(f"Level {level}: 不保留输入特征到下一层输入")


                # 遍历当前层配置的生成器
                for gen_config in layer_generators_config:
                    generators_to_run: list[tuple[str, dict[str, Any]]] = [] # List of tuples: (gen_name, gen_params)

                    if isinstance(gen_config, str):
                        gen_name = gen_config
                        # Roo-Fix: 处理字符串形式的生成器配置
                        if gen_name == "time_series_features":
                            # 特殊处理：运行 time_series_features 组下所有启用的生成器
                            # 假设这些生成器在 self.generators 中已初始化
                            ts_group_generators = ['diff', 'lag', 'window', 'volatility'] # 定义组包含的生成器
                            for sub_gen_name in ts_group_generators:
                                if sub_gen_name in self.generators and self.generators[sub_gen_name].is_enabled:
                                    # 这些生成器使用它们自己的全局配置，不传递层级参数
                                    generators_to_run.append((sub_gen_name, {}))
                            if not generators_to_run:
                                self.logger.warning(f"Level {level}: Group '{gen_name}' specified, but no associated generators are enabled or found.")
                        elif gen_name == "base_features":
                             # "base_features" 代表原始输入，不在此处生成
                             self.logger.debug(f"Level {level}: 'base_features' specified, using current layer input.")
                             pass # 没有实际的生成器需要运行
                        elif gen_name in self.generators:
                            # 运行单个生成器，使用其全局配置
                            generators_to_run.append((gen_name, {}))
                        else:
                            # Roo-Fix: 未知生成器时抛出 ValueError
                            error_msg = f"Level {level}: Unknown generator name '{gen_name}' in string format"
                            self.logger.error(error_msg)
                            raise ValueError(error_msg)
                            # self.logger.warning(f"Level {level}: Unknown generator name '{gen_name}' in string format, skipping.")

                    elif isinstance(gen_config, dict) and len(gen_config) == 1:
                        # Roo-Fix: 处理字典形式的生成器配置 {'gen_name': {params...}}
                        gen_name = next(iter(gen_config.keys()))
                        gen_params = gen_config[gen_name]
                        if not isinstance(gen_params, dict):
                             # Roo-Fix: 无效参数格式时抛出 ValueError
                             error_msg = f"Level {level}: Invalid parameters format for generator '{gen_name}' (expected dict): {gen_params}"
                             self.logger.error(error_msg)
                             raise ValueError(error_msg)
                             # self.logger.warning(f"Level {level}: Invalid parameters format for generator '{gen_name}' (expected dict): {gen_params}, skipping.")
                             # continue
                        if gen_name in self.generators:
                            generators_to_run.append((gen_name, gen_params))
                        else:
                             # Roo-Fix: 未知生成器时抛出 ValueError
                             error_msg = f"Level {level}: Unknown generator name '{gen_name}' in dict format"
                             self.logger.error(error_msg)
                             raise ValueError(error_msg)
                             # self.logger.warning(f"Level {level}: Unknown generator name '{gen_name}' in dict format, skipping.")
                    else:
                        # Roo-Fix: 无效配置格式时抛出 ValueError
                        error_msg = f"Level {level}: 无效的生成器配置格式: {gen_config}"
                        self.logger.error(error_msg)
                        raise ValueError(error_msg)
                        # self.logger.warning(f"Level {level}: 无效的生成器配置格式: {gen_config}，跳过")
                        # continue

                    # --- 执行识别出的生成器 ---
                    for run_gen_name, run_gen_params in generators_to_run:
                        if run_gen_name not in self.generators:
                             # Roo-Fix: 内部错误时抛出 ValueError
                             error_msg = f"Level {level}: Internal error - generator '{run_gen_name}' not found in self.generators."
                             self.logger.error(error_msg)
                             raise ValueError(error_msg)
                             # continue # 或者抛出异常

                        generator = self.generators[run_gen_name]
                        # 再次检查生成器是否启用（可能冗余，但更安全）
                        if not generator.is_enabled:
                            self.logger.info(f"Level {level}: Generator '{run_gen_name}' is not enabled, skipping.")
                            continue

                        gen_start_time = time.time()
                        try:
                            # 准备 kwargs 给特定生成器
                            specific_kwargs = run_gen_params.copy() # 从层级配置获取参数

                            # 如果是 InteractionFeatureGenerator (或其他需要目标序列的生成器)
                            # 并且 target_series 可用，则添加到 kwargs
                            # 需要导入 InteractionFeatureGenerator 类才能使用 isinstance
                            # from src.data.feature_engineering.interaction_features import InteractionFeatureGenerator # 假设已在文件顶部导入
                            if isinstance(generator, InteractionFeatureGenerator) and target_series is not None:
                                specific_kwargs['target_series'] = target_series
                                self.logger.info(f"Level {level}: 将目标序列传递给 '{run_gen_name}'")

                            # 使用当前层输入调用 generate，并传递特定参数（如果存在）
                            if specific_kwargs: # 如果有特定参数 (包括可能添加的 target_series)
                                # 只记录键以避免过长日志，特别是当 target_series 很大时
                                log_kwargs_keys = {k:type(v).__name__ for k,v in specific_kwargs.items()}
                                self.logger.info(f"Level {level}: Calling '{run_gen_name}' with specific parameters (keys/types): {log_kwargs_keys}")
                                new_features = generator.generate(current_layer_input, **specific_kwargs)
                            else: # specific_kwargs 为空 (即 run_gen_params 为空且未使用 target_series)
                                self.logger.info(f"Level {level}: Calling '{run_gen_name}' with its default parameters.")
                                default_params_to_pass = {}
                                # 特别处理 diff_features
                                if run_gen_name == 'diff' and isinstance(generator, DiffFeatureGenerator):
                                    # 尝试从全局配置的 time_series_features.diff_features 获取 orders
                                    if hasattr(self.config, 'feature_engineering') and \
                                       hasattr(self.config.feature_engineering, 'time_series_features') and \
                                       self.config.feature_engineering.time_series_features is not None and \
                                       hasattr(self.config.feature_engineering.time_series_features, 'diff_features') and \
                                       self.config.feature_engineering.time_series_features.diff_features is not None and \
                                       hasattr(self.config.feature_engineering.time_series_features.diff_features, 'orders'):
                                        default_params_to_pass['orders'] = self.config.feature_engineering.time_series_features.diff_features.orders
                                        self.logger.info(f"Level {level}: For '{run_gen_name}', using global diff_features orders: {default_params_to_pass['orders']}")
                                elif run_gen_name == 'lag' and isinstance(generator, LagFeatureGenerator):
                                    if hasattr(self.config, 'feature_engineering') and \
                                       hasattr(self.config.feature_engineering, 'time_series_features') and \
                                       self.config.feature_engineering.time_series_features is not None and \
                                       hasattr(self.config.feature_engineering.time_series_features, 'lag_features') and \
                                       self.config.feature_engineering.time_series_features.lag_features is not None:
                                        lag_cfg = self.config.feature_engineering.time_series_features.lag_features
                                        if hasattr(lag_cfg, 'max_lag'):
                                            default_params_to_pass['max_lag'] = lag_cfg.max_lag
                                        if hasattr(lag_cfg, 'step'):
                                            default_params_to_pass['step'] = lag_cfg.step
                                        if default_params_to_pass:
                                             self.logger.info(f"Level {level}: For '{run_gen_name}', using global lag_features params: {default_params_to_pass}")
                                elif run_gen_name == 'window' and isinstance(generator, WindowFeatureGenerator):
                                    if hasattr(self.config, 'feature_engineering') and \
                                       hasattr(self.config.feature_engineering, 'time_series_features') and \
                                       self.config.feature_engineering.time_series_features is not None and \
                                       hasattr(self.config.feature_engineering.time_series_features, 'window_features') and \
                                       self.config.feature_engineering.time_series_features.window_features is not None:
                                        window_cfg = self.config.feature_engineering.time_series_features.window_features
                                        if hasattr(window_cfg, 'window_sizes'):
                                            default_params_to_pass['window_sizes'] = window_cfg.window_sizes
                                        if hasattr(window_cfg, 'stats'):
                                            default_params_to_pass['stats'] = window_cfg.stats
                                        if default_params_to_pass:
                                             self.logger.info(f"Level {level}: For '{run_gen_name}', using global window_features params: {default_params_to_pass}")
                                elif run_gen_name == 'volatility' and isinstance(generator, VolatilityFeatureGenerator) and \
                                     hasattr(self.config, 'feature_engineering') and \
                                     hasattr(self.config.feature_engineering, 'time_series_features') and \
                                     self.config.feature_engineering.time_series_features is not None and \
                                     hasattr(self.config.feature_engineering.time_series_features, 'volatility_features') and \
                                     self.config.feature_engineering.time_series_features.volatility_features is not None:
                                        vol_cfg = self.config.feature_engineering.time_series_features.volatility_features
                                        # 获取所有 volatility 需要的参数
                                        vol_params = ['model_type', 'p', 'q', 'dist', 'scale_threshold']
                                        for param in vol_params:
                                            if hasattr(vol_cfg, param):
                                                default_params_to_pass[param] = getattr(vol_cfg, param)
                                        if default_params_to_pass:
                                             self.logger.info(f"Level {level}: For '{run_gen_name}', using global volatility_features params: {default_params_to_pass}")

                                if default_params_to_pass:
                                    new_features = generator.generate(current_layer_input, **default_params_to_pass)
                                else:
                                    new_features = generator.generate(current_layer_input) # 真正的无参数调用

                            gen_duration = time.time() - gen_start_time
                            if new_features is not None and new_features.shape[1] > 0:
                                if new_features.shape[0] != current_layer_input.shape[0]:
                                    raise ValueError(f"Generator '{run_gen_name}' output samples ({new_features.shape[0]}) mismatch input ({current_layer_input.shape[0]})")

                                feature_names = generator.get_feature_names()
                                # Roo-Fix: 特征数量与名称数量不匹配时抛出 ValueError
                                if len(feature_names) != new_features.shape[1]:
                                    error_msg = (f"Level {level}: Generator '{run_gen_name}' feature count ({new_features.shape[1]}) "
                                                 f"mismatch name count ({len(feature_names)})!")
                                    self.logger.error(error_msg)
                                    raise ValueError(error_msg)
                                    # self.logger.error(f"Level {level}: Generator '{run_gen_name}' feature count ({new_features.shape[1]}) mismatch name count ({len(feature_names)})!")
                                    # # 使用通用名称作为后备
                                    # feature_names = [f"{run_gen_name}_lvl{level}_f{i}" for i in range(new_features.shape[1])]

                                current_layer_derived_features.append(new_features)
                                current_layer_derived_names.extend(feature_names)
                                next_layer_input_components.append(new_features)
                                # next_layer_input_names.extend(feature_names) # 如果需要跟踪下一层名称
                                layer_derived_feature_counts[level][run_gen_name] = new_features.shape[1]
                                self.logger.info(f"Level {level}: Generator '{run_gen_name}' successfully generated {new_features.shape[1]} features | Duration: {gen_duration:.3f}s")
                            else:
                                layer_derived_feature_counts[level][run_gen_name] = 0
                                self.logger.info(f"Level {level}: Generator '{run_gen_name}' did not generate features | Duration: {gen_duration:.3f}s")

                        except Exception as e:
                            self.logger.error(f"Level {level}: Generator '{run_gen_name}' failed: {e}", exc_info=True)
                            raise ValueError(f"Level {level} Generator '{run_gen_name}' failed") from e


                # --- 合并当前层新生成的特征，并加入最终集合 ---
                if current_layer_derived_features:
                    level_derived_combined_tensor = torch.cat(current_layer_derived_features, dim=1)
                    final_feature_collection.append(level_derived_combined_tensor)
                    final_feature_names_collection.append(current_layer_derived_names) # 添加当前层的新名称
                    self.logger.info(f"Level {level}: 共生成 {level_derived_combined_tensor.shape[1]} 个新衍生特征 (by explicit generators in this level's loop)，已添加到最终特征集")
                    # 记录详细构成
                    counts_str = ", ".join([f"{name}: {count}" for name, count in layer_derived_feature_counts[level].items() if count > 0])
                    self.logger.info(f"Level {level} Derived Feature Counts (by explicit generators): [{counts_str}]")
                else:
                    # 如果是Level 0且其生成器包含 "base_features"（意味着时间特征等已在输入中）
                    # 则此日志表明没有 *额外* 的衍生特征由 Level 0 内的其他显式生成器产生。
                    # process() 方法已经记录了 Level 0 的完整构成。
                    is_level_0_with_base_features = False
                    if level == 0 and any(g == "base_features" for g in layer_generators_config if isinstance(g, str)):
                        # layer_generators_config is layer_config.generators
                        # layer_config is an element from sorted_layers, which comes from feature_eng.layers
                        # We need to access the original config for this level to check its generators list
                        # This is already available as layer_generators_config
                        is_level_0_with_base_features = True

                    if is_level_0_with_base_features:
                        self.logger.info(f"Level {level}: 'base_features' generator specified. Initial features (incl. time features if any) are carried over. No *additional* derivatives by other explicit generators in this level's loop.")
                    else:
                        self.logger.warning(f"Level {level}: 未生成任何新的衍生特征 (by explicit generators in this level's loop).")

                # --- 准备下一层的输入 ---
                if next_layer_input_components:
                    merge_next_input_start = time.time()
                    current_layer_input = torch.cat(next_layer_input_components, dim=1)
                    self.logger.info(f"Level {level}: 下一层输入准备完成 | 维度: {current_layer_input.shape} | 耗时: {time.time()-merge_next_input_start:.3f}s")
                else:
                    # 如果下一层没有输入（例如当前层不保留输入且未生成新特征），则停止处理
                    self.logger.warning(f"Level {level}: 未能构建下一层的输入，特征工程提前结束")
                    break # 退出层级循环

            # --- 4. 合并最终特征集 ---
            if not final_feature_collection:
                error_msg = "特征工程未产生任何最终特征，请检查配置和实现"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            merge_final_start = time.time()
            final_combined_features = torch.cat(final_feature_collection, dim=1)
            merge_final_duration = time.time() - merge_final_start

            # --- 5. 组合最终的特征名称列表 ---
            # 这个列表只包含由 enhance_features 各层级生成器产生的衍生特征名称
            final_derived_names = []
            for names_list in final_feature_names_collection:
                final_derived_names.extend(names_list)

            # --- 6. 记录总结信息 ---
            total_duration = time.time() - processing_start
            self.logger.info(
                f"\n=== 层级特征工程完成 (enhance_features) ==="
                f"\n- 总耗时: {total_duration:.3f}s"
                f"\n- 最终特征维度: {final_combined_features.shape}"
                f"\n- 最终合并耗时: {merge_final_duration:.3f}s"
            )
            # 记录最终特征构成摘要
            summary_str = []
            if keep_original_in_final:
                 summary_str.append(f"Level 0 (Original): {original_features.shape[1]}")
            for level, counts in layer_derived_feature_counts.items():
                 total_derived = sum(counts.values())
                 if total_derived > 0:
                     summary_str.append(f"Derived Level {level}: {total_derived}")
            self.logger.info(f"最终特征构成: [{', '.join(summary_str)}]")

            # 返回最终组合的特征张量 和 对应的衍生特征名称列表
            # 注意：返回的张量 final_combined_features 包含了根据 keep_original_in_final 添加的 Level 0 特征
            # 但是返回的名称列表 final_derived_names *只包含* 衍生特征的名称
            # process 方法将负责根据 keep_original_in_final 组合 Level 0 名称和这里的衍生名称
            return final_combined_features, final_derived_names

        except Exception as e:
            error_msg = f"层级特征增强失败: {e!s}"
            self.logger.error(error_msg, exc_info=True)
            raise ValueError(error_msg) from e

# Roo-Fix: 移除不再需要的 _should_keep_original 方法
